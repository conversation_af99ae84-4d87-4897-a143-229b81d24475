pluginManagement {
    repositories {
//        google {
//            content {
//                includeGroupByRegex("com\\.android.*")
//                includeGroupByRegex("com\\.google.*")
//                includeGroupByRegex("androidx.*")
//            }
//        }
        maven {
            setUrl("http://192.168.1.106:8081/repository/google-maven/")
            isAllowInsecureProtocol = true
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
//        mavenCentral()
        maven {
            setUrl("http://192.168.1.106:8081/repository/google-maven-central/")
            isAllowInsecureProtocol = true
        }
//        gradlePluginPortal()
        maven {
            setUrl("http://192.168.1.106:8081/repository/google-plugin/")
            isAllowInsecureProtocol = true
        }
//        maven("https://jitpack.io")
//        maven {
//            setUrl("http://192.168.1.106:8081/repository/jitpack/")
//            isAllowInsecureProtocol = true
//        }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
//        google()
        maven {
            setUrl("http://192.168.1.106:8081/repository/google-maven/")
            isAllowInsecureProtocol = true
        }
//        mavenCentral()
        maven {
            setUrl("http://192.168.1.106:8081/repository/google-maven-central/")
            isAllowInsecureProtocol = true
        }
//        maven("https://jitpack.io")
        maven {
            setUrl("http://192.168.1.106:8081/repository/jitpack/")
            isAllowInsecureProtocol = true
        }
    }
}

rootProject.name = "Dami Credit"
include(":app")
