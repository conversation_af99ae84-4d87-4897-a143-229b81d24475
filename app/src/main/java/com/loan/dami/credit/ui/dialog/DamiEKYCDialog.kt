package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiImage

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DamiEKYCDialog(call: (type: Int) -> Unit) {
    ModalBottomSheet(onDismissRequest = { call(0) }, containerColor = Color.White) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { call(1) }
                .padding(horizontal = 40.dp, vertical = 20.dp)) {
            DamiImage(modifier = Modifier.size(30.dp), id = R.mipmap.icon_camera)
            Text(modifier = Modifier.align(Alignment.Center), text = "Photograph", fontSize = 18.sp, lineHeight = 22.sp, color = Color(0xff29292a), fontWeight = FontWeight.Medium)
        }
        HorizontalDivider(modifier = Modifier.padding(horizontal = 20.dp), color = Color(0xffe7e7e7))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { call(2) }
                .padding(horizontal = 40.dp, vertical = 20.dp)) {
            DamiImage(modifier = Modifier.size(30.dp), id = R.mipmap.icon_photo)
            Text(modifier = Modifier.align(Alignment.Center), text = "Photo Album", fontSize = 18.sp, lineHeight = 22.sp, color = Color(0xff29292a), fontWeight = FontWeight.Medium)
        }
        HorizontalDivider(modifier = Modifier.padding(horizontal = 20.dp), color = Color(0xffe7e7e7))
        Text(
            modifier = Modifier
                .clickable { call(0) }
                .padding(20.dp)
                .align(Alignment.CenterHorizontally), text = "Cancel", fontSize = 18.sp, lineHeight = 22.sp, color = Color(0xffd1d1d1))
    }
}