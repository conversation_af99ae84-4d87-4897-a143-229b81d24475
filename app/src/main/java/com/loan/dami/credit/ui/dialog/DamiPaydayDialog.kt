package com.loan.dami.credit.ui.dialog

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.loan.dami.credit.R
import com.loan.dami.credit.bean.DamiTagBean
import com.loan.dami.credit.config.DamiImage

@Composable
fun DamiPaydayDialog(title: String, list: List<DamiTagBean>, onDismiss: () -> Unit, onResult: (String, String, String) -> Unit) {
    val selectText = "Please select"
    var typeState by remember { mutableIntStateOf(1) }
    var oneState by remember { mutableStateOf(selectText) }
    var twoState by remember { mutableStateOf(selectText) }
    val twoList = remember { mutableListOf<DamiTagBean>() }

    val click: (type: Int, item: DamiTagBean?) -> Unit = { type, item ->
        if (item == null) {
            typeState = when {
                type == 2 && twoList.isEmpty() -> 1
                else -> if (typeState != type) type else 4
            }
        } else {
            when (type) {
                1 -> {
                    oneState = item.name
                    twoList.clear()
                    item.note?.also { twoList.addAll(it) }
                    twoState = selectText
                    typeState = 2
                }

                2 -> {
                    twoState = item.name
                    typeState = 4
                    onResult(oneState, twoState, item.type)
                }
            }
        }
    }

    Dialog(onDismissRequest = onDismiss, properties = DialogProperties(usePlatformDefaultWidth = false)) {
        Column(modifier = Modifier.fillMaxSize()) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .clickable(onClick = onDismiss)
            )
            Column(
                modifier = Modifier
                    .clip(RoundedCornerShape(topEnd = 16.dp, topStart = 16.dp))
                    .background(color = Color.White)
            ) {
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color(0xfff5f9fa))
                        .padding(vertical = 17.dp)
                        .align(Alignment.CenterHorizontally),
                    text = title,
                    fontSize = 16.sp,
                    lineHeight = 18.sp,
                    color = Color(0xff3e3e3e),
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center
                )
                Column(modifier = Modifier.padding(start = 20.dp, bottom = 10.dp)) {
                    Text(modifier = Modifier.padding(top = 14.dp), text = "Type", fontSize = 14.sp, color = Color(0xff3e3e3e), lineHeight = 16.sp, fontWeight = FontWeight.Bold)
                    Row(
                        modifier = Modifier
                            .clickable { click(1, null) }
                            .padding(top = 8.dp, bottom = 8.dp, end = 20.dp), verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            modifier = Modifier.weight(1f),
                            text = oneState,
                            color = Color(if (oneState == selectText) 0xffd1d1d1 else 0xff016b76),
                            fontSize = 14.sp,
                            lineHeight = 18.sp
                        )
                        DamiImage(modifier = Modifier.size(16.dp), id = if (typeState == 1) R.mipmap.personal_arrow else R.mipmap.personal_arrow_r)
                    }
                    HorizontalDivider(color = Color(0xff016b76))
                    AnimatedVisibility(typeState == 1) {
                        LazyColumn(
                            modifier = Modifier
                                .heightIn(max = 200.dp)
                                .background(Color(0xfff5f9fa), shape = RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp))
                                .padding(5.dp)
                        ) {
                            items(items = list) { item ->
                                Text(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable { click(1, item) }
                                        .padding(5.dp),
                                    text = item.name,
                                    color = Color(if (oneState == item.name) 0xff016b76 else 0xffb0b0b0),
                                    fontWeight = if (oneState == item.name) FontWeight.Medium else FontWeight.Normal,
                                    fontSize = 14.sp,
                                    lineHeight = 18.sp,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                    Text(modifier = Modifier.padding(top = 14.dp), text = "Date", fontSize = 14.sp, color = Color(0xff3e3e3e), lineHeight = 16.sp, fontWeight = FontWeight.Bold)
                    Row(
                        modifier = Modifier
                            .clickable { click(2, null) }
                            .padding(top = 8.dp, bottom = 8.dp, end = 20.dp), verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            modifier = Modifier.weight(1f),
                            text = twoState,
                            color = Color(if (twoState == selectText) 0xffd1d1d1 else 0xff016b76),
                            fontSize = 14.sp,
                            lineHeight = 18.sp
                        )
                        DamiImage(modifier = Modifier.size(16.dp), id = if (typeState == 2) R.mipmap.personal_arrow else R.mipmap.personal_arrow_r)
                    }
                    HorizontalDivider(color = Color(0xff016b76))
                    AnimatedVisibility(typeState == 2) {
                        LazyColumn(
                            modifier = Modifier
                                .heightIn(max = 200.dp)
                                .background(Color(0xfff5f9fa), shape = RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp))
                                .padding(5.dp)
                        ) {
                            items(items = twoList) { item ->
                                Text(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable { click(2, item) }
                                        .padding(5.dp),
                                    text = item.name,
                                    color = Color(if (twoState == item.name) 0xff016b76 else 0xffb0b0b0),
                                    fontWeight = if (twoState == item.name) FontWeight.Medium else FontWeight.Normal,
                                    fontSize = 14.sp,
                                    lineHeight = 18.sp,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }
            }
        }
    }

}