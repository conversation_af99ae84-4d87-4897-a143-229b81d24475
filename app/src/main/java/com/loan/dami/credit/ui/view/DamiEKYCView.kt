package com.loan.dami.credit.ui.view

import android.app.Activity.RESULT_OK
import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.bean.DamiEKYCBean
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiInput
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiTitle
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiEKYCModel
import com.loan.dami.credit.ui.dialog.DamiEKYCDialog
import com.loan.dami.credit.ui.dialog.DamiTimeDialog
import com.loan.dami.credit.ui.dialog.DamiVerifyDialog
import com.loan.dami.credit.ui.route.DamiEKYCRoute

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DamiEKYCView(route: DamiEKYCRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current
    val model = viewModel { DamiEKYCModel(route) }
    val state by model.state.collectAsState()
    LaunchedEffect(model) { model.kycData(controller) }

    val photoResult = rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            model.photoResult(context, controller, result.data?.data)
        }
    }

    var sheetState by remember { mutableStateOf(false) }

    if (sheetState) {
        DamiEKYCDialog {
            model.startTime = System.currentTimeMillis()
            sheetState = false
            if (it == 1) {
                model.navigateCamera(context, controller)
            } else if (it == 2) {
                photoResult.launch(Intent(Intent.ACTION_PICK).apply { setType("image/*") })
            }
        }
    }

    state.url?.let {
        DamiVerifyDialog(it) {
            model.closeDialog()
            if (it == 1) {
                controller.popBackStack(route, true)
            }
        }
    }

    BackHandler { model.backClick(controller) }
    DamiScrollView(model, title = "E-KYC", onBack = { model.backClick(controller) }, bottomBar = {
        AnimatedVisibility(state.showType == 2) {
            Text(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
                    .background(Color(0xff016b76), shape = CircleShape)
                    .clickDelay { sheetState = true }
                    .padding(14.dp),
                text = "Next",
                fontSize = 16.sp,
                lineHeight = 20.sp,
                color = Color.White,
                textAlign = TextAlign.Center
            )
        }
        AnimatedVisibility(state.showType == 3) {
            Text(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
                    .background(Color(0xff016b76), shape = CircleShape)
                    .clickDelay { model.submitEKYCData(context, controller) }
                    .padding(14.dp),
                text = "Confirm And Submit",
                fontSize = 16.sp,
                lineHeight = 20.sp,
                color = Color.White,
                textAlign = TextAlign.Center
            )
        }
    }) {
        AnimatedVisibility(state.showType == 1) {
            Box(
                modifier = Modifier
                    .background(Color(0xff016b76))
                    .padding(horizontal = 20.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                DamiFillImage(id = R.mipmap.top_bg_s)
                Text(
                    modifier = Modifier.padding(horizontal = 20.dp),
                    text = "Select an ID to verify your identity",
                    fontSize = 16.sp,
                    lineHeight = 19.sp,
                    color = Color(0xff2a292a),
                    fontWeight = FontWeight.Medium
                )
            }
        }
        AnimatedVisibility(state.showType == 2) {
            Box(
                modifier = Modifier
                    .background(Color(0xff016b76))
                    .padding(horizontal = 20.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                DamiFillImage(id = R.mipmap.top_bg_b)
                Text(
                    modifier = Modifier.padding(horizontal = 20.dp),
                    text = "Fill in the identity information\ntruly and accurately, and the loan\nsucces rate is 90%",
                    fontSize = 16.sp,
                    lineHeight = 19.sp,
                    color = Color(0xff2a292a),
                    fontWeight = FontWeight.Medium
                )
            }
        }
        AnimatedVisibility(state.showType == 3) {
            Box(
                modifier = Modifier
                    .background(Color(0xff016b76))
                    .padding(horizontal = 20.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                DamiFillImage(id = R.mipmap.top_bg_b)
                Text(
                    modifier = Modifier.padding(horizontal = 20.dp),
                    text = "Please check your ID information\ncorrectly, once submitted it is not\nchanged again",
                    fontSize = 16.sp,
                    lineHeight = 19.sp,
                    color = Color(0xff2a292a),
                    fontWeight = FontWeight.Medium
                )
            }
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xff016b76))
                .padding(top = 16.dp)
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(vertical = 12.dp)
        ) {
            AnimatedVisibility(state.showType == 1) {
                Column {
                    DamiTitle(modifier = Modifier.padding(vertical = 8.dp), title = "RECOMMENDED ID TYPE")
                    state.bean?.cardTypeList?.firstOrNull()?.forEach { value ->
                        Row(
                            modifier = Modifier
                                .padding(end = 20.dp, top = 6.dp, bottom = 6.dp)
                                .clickable { model.setType(value) }, verticalAlignment = Alignment.CenterVertically
                        ) {
                            DamiImage(
                                modifier = Modifier
                                    .background(Color(0xff016b76), shape = RoundedCornerShape(topEnd = 20.dp, bottomEnd = 20.dp))
                                    .padding(start = 20.dp, top = 5.dp, end = 5.dp, bottom = 5.dp)
                                    .size(36.dp), id = R.mipmap.ekyc_icon
                            )
                            Text(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 12.dp), text = value, fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium
                            )
                            DamiImage(modifier = Modifier.size(36.dp), id = R.mipmap.ekyc_edit)
                        }
                    }
                    state.bean?.cardTypeList?.lastOrNull()?.let { list ->
                        var otherState by remember { mutableStateOf(false) }
                        Row(
                            modifier = Modifier
                                .padding(vertical = 8.dp)
                                .clickable { otherState = !otherState }, verticalAlignment = Alignment.CenterVertically
                        ) {
                            DamiTitle(title = "OTHER OPTIONS")
                            Spacer(Modifier.weight(1f))
                            DamiImage(
                                modifier = Modifier
                                    .padding(end = 20.dp)
                                    .size(20.dp), id = if (otherState) R.mipmap.icon_ekyc_arrow_b else R.mipmap.icon_ekyc_arrow_r
                            )
                        }
                        if (otherState) {
                            list.forEach { value ->
                                Row(
                                    modifier = Modifier
                                        .padding(end = 20.dp, top = 6.dp, bottom = 6.dp)
                                        .clickable { model.setType(value) }, verticalAlignment = Alignment.CenterVertically
                                ) {
                                    DamiImage(
                                        modifier = Modifier
                                            .background(Color(0xff016b76), shape = RoundedCornerShape(topEnd = 20.dp, bottomEnd = 20.dp))
                                            .padding(start = 20.dp, top = 5.dp, end = 5.dp, bottom = 5.dp)
                                            .size(36.dp), id = R.mipmap.ekyc_icon
                                    )
                                    Text(
                                        modifier = Modifier
                                            .weight(1f)
                                            .padding(horizontal = 12.dp), text = value, fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium
                                    )
                                    DamiImage(modifier = Modifier.size(36.dp), id = R.mipmap.ekyc_edit)
                                }
                            }
                        }
                    }
                }
            }

            AnimatedVisibility(state.showType == 2) {
                DamiFillImage(modifier = Modifier.padding(top = 8.dp, bottom = 8.dp, end = 20.dp), id = R.mipmap.img_ekyc)
            }
            AnimatedVisibility(state.showType == 3) {
                Column {
                    state.ekyc?.apply {
                        DamiEKYCItem(title = "Full Name", value = name, imeAction = ImeAction.Next) { name = it }
                        DamiEKYCItem(title = "ID No.", value = id_number) { id_number = it }
                        DamiEKYCItem(bean = this, title = "Date of Birth", value = birthday, enable = false)
                    }
                }
            }
        }
    }
}

@Composable
fun DamiEKYCItem(bean: DamiEKYCBean? = null, title: String, value: String, enable: Boolean = true, imeAction: ImeAction = ImeAction.Done, onValue: (String) -> Unit = {}) {
    var valueState by remember { mutableStateOf(value) }
    var timeState by remember { mutableStateOf(false) }
    if (timeState) {
        bean?.apply {
            DamiTimeDialog(d = day.toIntOrNull() ?: 1, m = month.toIntOrNull() ?: 1, y = year.toIntOrNull() ?: 1980, onDismiss = { timeState = false }) { y, m, d ->
                timeState = false
                valueState = "$y/$m/$d"
                birthday = valueState
                day = d
                month = m
                year = y
            }
        }
    }
    Column(
        modifier = Modifier
            .padding(horizontal = 20.dp, vertical = 6.dp)
            .border(1.dp, Color(0xff82c3bd), RoundedCornerShape(4.dp))
            .background(Color.White, RoundedCornerShape(4.dp))
            .clickDelay(enable = !enable) { timeState = true }
            .padding(12.dp)
    ) {
        Text(modifier = Modifier.padding(vertical = 4.dp), text = title, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
        HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp), color = Color(0xffd1d1d1))
        DamiInput(
            value = valueState,
            onValueChange = {
                valueState = it
                onValue(it)
            },
            enabled = enable,
            imeAction = imeAction,
            endView = { if (!enable) DamiImage(modifier = Modifier.size(14.dp, 8.dp), id = R.mipmap.personal_arrow) }
        )
    }
}