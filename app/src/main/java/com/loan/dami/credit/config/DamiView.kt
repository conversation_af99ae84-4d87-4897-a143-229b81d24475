package com.loan.dami.credit.config

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.keyframes
import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.ScrollableState
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.FocusState
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.isUnspecified
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.isUnspecified
import androidx.compose.ui.unit.sp
import coil3.compose.SubcomposeAsyncImage
import com.loan.dami.credit.BuildConfig
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.ui.dialog.DamiLoading
import kotlinx.coroutines.delay
import kotlin.math.roundToInt

@Composable
fun DamiTitle(modifier: Modifier = Modifier, title: String) {
    Text(
        modifier = modifier
            .background(Color(0xff016b76), shape = RoundedCornerShape(topEnd = 8.dp, bottomEnd = 8.dp))
            .padding(top = 2.dp, end = 2.dp, bottom = 2.dp)
            .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 6.dp, bottomEnd = 6.dp))
            .padding(3.dp)
            .background(Color(0xffec8944), RoundedCornerShape(topEnd = 5.dp, bottomEnd = 5.dp))
            .padding(bottom = 2.dp)
            .background(Color(0xff016b76), RoundedCornerShape(topEnd = 5.dp, bottomEnd = 5.dp))
            .padding(vertical = 5.dp, horizontal = 15.dp),
        text = title,
        fontSize = 16.sp,
        lineHeight = 19.sp,
        fontWeight = FontWeight.Medium,
        color = Color.White,
        fontStyle = FontStyle.Italic
    )
}

@Composable
fun DamiCode(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    textColor: Color = Color.Unspecified,
    length: Int = 6,
    boxMargin: Dp = 8.dp,
    borderColor: Color = Color(0xffe7e7e7),
    borderFocusColor: Color = Color(0xff016b76),
    enabled: Boolean = true,
    textStyle: TextStyle = TextStyle(fontSize = 20.sp),
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Center,
    keyboardOptions: KeyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    cursorBrush: Brush = SolidColor(Color(0xff016b76)),
    cipherMask: String = "",
    focusRequester: FocusRequester = FocusRequester(),
) {
    var focusState by remember { mutableStateOf(false) }
    val textFieldValue = TextFieldValue(text = value, selection = TextRange(value.length))
    var lastTextValue by remember { mutableStateOf(value) }

    BasicTextField(
        value = textFieldValue,
        onValueChange = {
            val newText = if (it.text.length <= length) {
                it.text
            } else {
                it.text.substring(0, length)
            }
            if (lastTextValue != newText) {
                lastTextValue = newText
                onValueChange(newText)
            }
        },
        modifier = Modifier
            .focusRequester(focusRequester)
            .onFocusChanged { focusState = it.hasFocus },
        enabled = enabled,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        singleLine = true,
        cursorBrush = cursorBrush
    ) {
        val width = (LocalConfiguration.current.screenWidthDp.dp - 120.dp) / length
        Row(modifier = modifier, horizontalArrangement = horizontalArrangement) {
            repeat(length) {
                if (boxMargin.value > 0 && it != 0) {
                    Spacer(modifier = Modifier.width(boxMargin))
                }
                val selection = it == value.length
                val text = value.getOrNull(it)?.toString() ?: ""

                Box(
                    modifier = Modifier.size(width = width, height = width),
                    contentAlignment = Alignment.Center
                ) {
                    val cursorRectState = remember { mutableStateOf(Rect(0f, 10f, 0f, 10f)) }
                    Text(
                        text = if (cipherMask.isNotEmpty() && text.isNotEmpty()) cipherMask else text,
                        modifier = Modifier.cursor(
                            cursorBrush = cursorBrush,
                            cursorRect = cursorRectState.value,
                            enabled = focusState && selection
                        ),
                        color = textColor,
                        onTextLayout = { result -> cursorRectState.value = result.getCursorRect(0) },
                        style = textStyle
                    )
                    HorizontalDivider(
                        modifier = Modifier.align(Alignment.BottomCenter),
                        thickness = 2.dp,
                        color = if ((focusState && selection) || text.isNotEmpty()) borderFocusColor else borderColor
                    )
                }
            }
        }
    }
}

internal fun Modifier.cursor(
    cursorBrush: Brush,
    cursorRect: Rect,
    enabled: Boolean
) = when {
    enabled -> composed {
        val cursorAlpha = remember { Animatable(1f) }
        val isBrushSpecified = !(cursorBrush is SolidColor && cursorBrush.value.isUnspecified)
        if (isBrushSpecified) {
            LaunchedEffect(cursorBrush) {
                cursorAlpha.animateTo(
                    0f, infiniteRepeatable(
                        animation = keyframes {
                            durationMillis = 1000
                            1f at 0
                            1f at 499
                            0f at 500
                            0f at 999
                        }
                    ))
            }
            drawWithContent {
                this.drawContent()
                val cursorAlphaValue = cursorAlpha.value.coerceIn(0f, 1f)
                if (cursorAlphaValue != 0f) {
                    val cursorWidth = 2.dp.toPx()
                    val cursorX = (cursorRect.left + cursorWidth / 2).coerceAtMost(size.width - cursorWidth / 2)
                    drawLine(
                        cursorBrush,
                        Offset(cursorX, cursorRect.top),
                        Offset(cursorX, cursorRect.bottom),
                        alpha = cursorAlphaValue,
                        strokeWidth = cursorWidth
                    )
                }
            }
        } else {
            Modifier
        }
    }

    else -> this
}

@Composable
fun DamiRating(modifier: Modifier = Modifier, value: Int, enabled: Boolean = true, onValueChange: (Int) -> Unit) {
    val rating by remember(5) { mutableFloatStateOf(1f / 5) }
    val newRating by rememberUpdatedState(newValue = value)
    var progress by remember(5) { mutableFloatStateOf(newRating * rating) }

    var size by remember { mutableIntStateOf(0) }
    var sizeDp by remember { mutableStateOf(0.dp) }
    val ratingState by rememberUpdatedState(newValue = progress)

    val sizeState = remember(size) {
        ScrollableState {
            progress = maxOf(0f, minOf(ratingState + it / size, 1f))
            val rating = (progress / rating).roundToInt()
            if (rating != newRating) onValueChange(rating)
            it
        }
    }
    Row(
        modifier = modifier
            .fillMaxWidth()
            .layout { measurable, constraints ->
                val placeable = measurable.measure(constraints)
                size = placeable.width
                sizeDp = (placeable.width / 5).toDp()
                layout(placeable.width, placeable.height) { placeable.placeRelative(0, 0) }
            }
            .scrollable(state = sizeState, orientation = Orientation.Horizontal, enabled = enabled),
        horizontalArrangement = Arrangement.Center
    ) {
        repeat(5) { index ->
            if (index > 0) Spacer(modifier = Modifier.weight(1f))
            Icon(
                imageVector = Icons.Filled.Star,
                tint = Color(if (value > index) 0xffF8AC36 else 0xff707070),
                modifier = Modifier
                    .size(sizeDp)
                    .clickable(interactionSource = remember { MutableInteractionSource() }, indication = null, enabled = enabled) {
                        onValueChange(index + 1)
                    },
                contentDescription = ""
            )
        }
    }
}

@Composable
fun DamiSizeText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    lineHeight: TextUnit = TextUnit.Unspecified,
    style: TextStyle = LocalTextStyle.current,
) {
    var resizedSize by remember { mutableStateOf(if (fontSize == TextUnit.Unspecified) style.fontSize else fontSize) }
    var draw by remember { mutableStateOf(false) }

    Text(
        text = text,
        color = color,
        modifier = modifier.drawWithContent { if (draw) drawContent() },
        softWrap = false,
        fontSize = resizedSize,
        fontStyle = fontStyle,
        fontWeight = fontWeight,
        textAlign = textAlign,
        lineHeight = lineHeight,
        maxLines = maxLines,
        style = style,
        onTextLayout = { result ->
            if (result.didOverflowWidth) {
                if (resizedSize.isUnspecified) {
                    resizedSize = fontSize
                }
                resizedSize *= 0.95
            } else {
                draw = true
            }
        }
    )
}

@Composable
fun DamiInput(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    textAlign: TextAlign = TextAlign.Start,
    textStyle: TextStyle = TextStyle(fontSize = 16.sp, color = Color.Black, lineHeight = 18.sp, fontWeight = FontWeight.Medium, textAlign = textAlign),
    keyboardType: KeyboardType = KeyboardType.Text,
    imeAction: ImeAction = ImeAction.Done,
    enabled: Boolean = true,
    placeholder: String = if (!enabled) "Please select" else "Please enter",
    onFocus: (FocusState) -> Unit = {},
    startView: @Composable (() -> Unit)? = null,
    endView: @Composable (() -> Unit)? = null,
    focusRequester: FocusRequester = FocusRequester(),
) {

    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        startView?.invoke()
        BasicTextField(
            modifier = Modifier
                .weight(1f)
                .padding(start = if (startView != null) 10.dp else 0.dp, end = if (endView != null) 10.dp else 0.dp, top = 5.dp, bottom = 5.dp)
                .focusRequester(focusRequester)
                .onFocusChanged(onFocus),
            value = value,
            onValueChange = onValueChange,
            singleLine = true,
            textStyle = textStyle,
            enabled = enabled,
            keyboardOptions = KeyboardOptions(keyboardType = keyboardType, imeAction = imeAction),
            decorationBox = { innerTextField ->
                if (value.isEmpty()) {
                    DamiSizeText(modifier = Modifier.padding(top = 2.dp), text = placeholder, fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xffd1d1d1), textAlign = textStyle.textAlign)
                }
                innerTextField()
            },
        )
        endView?.invoke()
    }
}

@Composable
fun DamiToast(state: SnackbarHostState) {
    val data = state.currentSnackbarData
    LaunchedEffect(data) {
        data?.apply {
            delay(2000L)
            dismiss()
        }
    }
    SnackbarHost(state) {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text(
                modifier = Modifier
                    .padding(20.dp)
                    .background(Color(0xB3000000), shape = RoundedCornerShape(10.dp))
                    .padding(vertical = 10.dp, horizontal = 15.dp),
                text = it.visuals.message,
                color = Color.White,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun DamiImage(modifier: Modifier = Modifier, contentScale: ContentScale = ContentScale.Fit, bitmap: ImageBitmap) {
    Image(modifier = modifier, contentScale = contentScale, bitmap = bitmap, contentDescription = "")
}

@Composable
fun DamiImage(modifier: Modifier = Modifier, contentScale: ContentScale = ContentScale.Fit, id: Int) {
    Image(modifier = modifier, contentScale = contentScale, painter = painterResource(id), contentDescription = "")
}

@Composable
fun DamiFillImage(modifier: Modifier = Modifier, id: Int) {
    DamiImage(modifier = modifier.fillMaxWidth(), contentScale = ContentScale.FillWidth, id = id)
}

@Composable
fun DamiFillImage(modifier: Modifier = Modifier, contentScale: ContentScale = ContentScale.FillWidth, bitmap: ImageBitmap) {
    DamiImage(modifier = modifier.fillMaxWidth(), contentScale = contentScale, bitmap = bitmap)
}

@Composable
fun DamiCoil(modifier: Modifier = Modifier, contentScale: ContentScale = ContentScale.Fit, color: Color = Color(0xB3000000), model: Any?, empty: Int? = null) {
    val url = if (model is String) {
        model.replace("ph-dami-credit-files-prod.oss-ap-southeast-6.aliyuncs.com", "*************/oss-ph-dc").replace("https://ph-fenqi-files-prod.oss-ap-southeast-6.aliyuncs.com", "http://*************/oss-ph-fq")
    } else model
    SubcomposeAsyncImage(
        modifier = modifier,
        contentScale = contentScale,
        model = url,
        contentDescription = "",
        loading = {
            empty?.let { DamiImage(modifier, contentScale, it) } ?: Box(modifier = modifier.background(color), contentAlignment = Alignment.Center) {
                CircularProgressIndicator(modifier = Modifier.padding(5.dp), color = Color.White, strokeWidth = 2.dp)
            }
        },
        error = { empty?.let { DamiImage(modifier, contentScale, it) } }
    )
}

@Composable
fun DamiFillCoil(modifier: Modifier = Modifier, model: Any?, color: Color = Color(0xB3000000), empty: Int? = null) {
    DamiCoil(modifier = modifier.fillMaxWidth(), contentScale = ContentScale.FillWidth, color = color, model = model, empty = empty)
}

@Composable
fun <T : DamiState> DamiScrollView(
    model: DamiModel<T>,
    title: String? = null,
    titleColor: Color = Color.White,
    topBarColor: Color = Color(0xff016b76),
    bottomBarColor: Color = Color.Transparent,
    containerColor: Color = Color(0xffecf5fa),
    scrollState: ScrollState = rememberScrollState(),
    backImage: Int = R.mipmap.back_img,
    onBack: (() -> Unit)? = null,
    backgroundImage: Int? = null,
    topAction: (@Composable RowScope.() -> Unit)? = null,
    topBar: (@Composable BoxScope.() -> Unit)? = null,
    bottomBar: (@Composable ColumnScope.() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit,
) {
    DamiView(model, title, titleColor, topBarColor, bottomBarColor, containerColor, backImage, onBack, backgroundImage, topAction, topBar, bottomBar) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(scrollState), content = content
        )
    }
}

@Composable
fun <T : DamiState> DamiView(
    model: DamiModel<T>,
    title: String? = null,
    titleColor: Color = Color.White,
    topBarColor: Color = Color(0xff016b76),
    bottomBarColor: Color = Color.Transparent,
    containerColor: Color = Color(0xffecf5fa),
    backImage: Int = R.mipmap.back_img,
    onBack: (() -> Unit)? = null,
    backgroundImage: Int? = null,
    topAction: (@Composable RowScope.() -> Unit)? = null,
    topBar: (@Composable BoxScope.() -> Unit)? = null,
    bottomBar: (@Composable ColumnScope.() -> Unit)? = null,
    content: @Composable (PaddingValues) -> Unit,
) {
    DamiView(title, titleColor, topBarColor, bottomBarColor, containerColor, backImage, onBack, backgroundImage, topAction, topBar, bottomBar, content)
    val loadingState by model.loading.collectAsState()
    if (loadingState) {
        DamiLoading()
    }
    val toastState by model.toast.collectAsState()
    val hostState = remember { SnackbarHostState() }
    LaunchedEffect(toastState) {
        snapshotFlow { toastState }
            .collect { value ->
                if (value.isNotEmpty()) {
                    model.toast("")
                    hostState.showSnackbar(value)
                }
            }
    }
    DamiToast(hostState)
}

@Composable
fun DamiScrollView(
    title: String? = null,
    titleColor: Color = Color.White,
    topBarColor: Color = Color(0xff016b76),
    bottomBarColor: Color = Color.Transparent,
    containerColor: Color = Color(0xffecf5fa),
    scrollState: ScrollState = rememberScrollState(),
    backImage: Int = R.mipmap.back_img,
    onBack: (() -> Unit)? = null,
    backgroundImage: Int? = null,
    topAction: (@Composable RowScope.() -> Unit)? = null,
    topBar: (@Composable BoxScope.() -> Unit)? = null,
    bottomBar: (@Composable ColumnScope.() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit,
) {
    DamiView(title, titleColor, topBarColor, bottomBarColor, containerColor, backImage, onBack, backgroundImage, topAction, topBar, bottomBar) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(scrollState), content = content
        )
    }
}

@Composable
fun DamiView(
    title: String? = null,
    titleColor: Color = Color.White,
    topBarColor: Color = Color(0xff016b76),
    bottomBarColor: Color = Color.Transparent,
    containerColor: Color = Color(0xffecf5fa),
    backImage: Int = R.mipmap.back_img,
    onBack: (() -> Unit)? = null,
    backgroundImage: Int? = null,
    topAction: (@Composable RowScope.() -> Unit)? = null,
    topBar: (@Composable BoxScope.() -> Unit)? = null,
    bottomBar: (@Composable ColumnScope.() -> Unit)? = null,
    content: @Composable (PaddingValues) -> Unit,
) {
    val keyManager = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    Scaffold(
        modifier = Modifier.pointerInput(keyManager) {
            detectTapGestures {
                keyManager?.hide()
                focusManager.clearFocus()
            }
        },
        topBar = {
            val modifier = Modifier
                .background(topBarColor)
                .statusBarsPadding()
                .padding(vertical = 10.dp)
                .fillMaxWidth()
            topBar?.let { bar ->
                Box(modifier) {
                    bar()
                    topAction?.also { Row(modifier = Modifier.align(Alignment.CenterEnd), verticalAlignment = Alignment.CenterVertically, content = it) }
                }
            } ?: title?.also { value ->
                val controller = LocalDamiController.current
                Box(modifier) {
                    DamiImage(
                        modifier = Modifier
                            .padding(start = 15.dp)
                            .clip(CircleShape)
                            .size(44.dp)
                            .clickDelay { onBack?.invoke() ?: controller.popBackStack() }
                            .padding(5.dp)
                            .align(Alignment.CenterStart),
                        id = backImage
                    )
                    DamiSizeText(
                        modifier = Modifier
                            .padding(horizontal = 50.dp)
                            .align(Alignment.Center),
                        text = value,
                        color = titleColor,
                        fontSize = 20.sp,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight.Bold
                    )
                    topAction?.also { Row(modifier = Modifier.align(Alignment.CenterEnd), verticalAlignment = Alignment.CenterVertically, content = it) }
                }
            }
        },
        containerColor = containerColor,
        bottomBar = {
            bottomBar?.let {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(if (bottomBarColor == Color.Transparent) 0.dp else 5.dp)
                        .background(bottomBarColor)
                        .navigationBarsPadding(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    content = it
                )
            }
        },
        content = {
            backgroundImage?.also { image -> DamiFillImage(id = image) }
            content(it)
        }
    )
}

internal fun Modifier.clickDelay(enable: Boolean = true, onClick: () -> Unit) = composed {
    var time by remember { mutableLongStateOf(0L) }
    clickable(enable) {
        val timeMillis = System.currentTimeMillis()
        if (timeMillis - 600 >= time) {
            time = timeMillis
            onClick()
        }
    }
}