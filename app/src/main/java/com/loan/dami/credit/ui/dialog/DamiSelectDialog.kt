package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.loan.dami.credit.config.DamiSizeText
import com.sd.lib.compose.wheel_picker.FVerticalWheelPicker
import com.sd.lib.compose.wheel_picker.FWheelPickerFocusVertical
import com.sd.lib.compose.wheel_picker.rememberFWheelPickerState

@Composable
fun DamiSelectDialog(title: String, data: List<String>, onResult: (Int) -> Unit) {

    val state = rememberFWheelPickerState()

    Dialog(onDismissRequest = { onResult(-1) }, properties = DialogProperties(usePlatformDefaultWidth = false)) {
        Column(modifier = Modifier.fillMaxSize()) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .clickable { onResult(-1) })
            Column(
                modifier = Modifier
                    .clip(RoundedCornerShape(topEnd = 16.dp, topStart = 16.dp))
                    .background(color = Color.White)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(10.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Button(onClick = { onResult(-1) }, colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent)) {
                        Text(text = "Cancel", color = Color(0xffb0b0b0), fontSize = 14.sp, lineHeight = 18.sp, fontWeight = FontWeight.Medium)
                    }
                    DamiSizeText(
                        modifier = Modifier.padding(horizontal = 5.dp).weight(1f),
                        text = title,
                        fontSize = 16.sp,
                        lineHeight = 20.sp,
                        color = Color(0xff2a292a),
                        fontWeight = FontWeight.Medium,
                        textAlign = TextAlign.Center
                    )
                    Button(colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent), onClick = {
                        onResult(state.currentIndex)
                    }) {
                        Text(text = "Done", color = Color(0xff2e9a6d), fontSize = 14.sp, lineHeight = 18.sp, fontWeight = FontWeight.Medium)
                    }
                }
                FVerticalWheelPicker(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 10.dp, horizontal = 20.dp),
                    state = state,
                    count = data.size,
                    itemHeight = 45.dp,
                    focus = { FWheelPickerFocusVertical(dividerColor = Color(0xffe7e7e7)) },
                    unfocusedCount = 2
                ) {
                    Text(text = data[it], fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a))
                }
            }
        }
    }
}