package com.loan.dami.credit.ui.view

import android.app.Activity.RESULT_OK
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.android.gms.auth.api.identity.GetPhoneNumberHintIntentRequest
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiCode
import com.loan.dami.credit.config.DamiConfigUtils
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiInput
import com.loan.dami.credit.config.DamiLoginCode
import com.loan.dami.credit.config.DamiRouteUtils
import com.loan.dami.credit.config.DamiView
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiLoginModel
import java.util.regex.Pattern

@Composable
fun DamiLoginView() {
    val context = LocalContext.current
    val keyboard = LocalSoftwareKeyboardController.current
    val controller = LocalDamiController.current
    val model = viewModel { DamiLoginModel() }
    val state = model.state.collectAsState().value
    val codeRequester = remember { FocusRequester() }

    val codeResult = rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.getStringExtra(SmsRetriever.EXTRA_SMS_MESSAGE)?.let {
                if (it.isNotEmpty()) {
                    Pattern.compile("\\d{6}").matcher(it).apply {
                        if (find()) {
                            model.setCode(controller, keyboard, group())
                        }
                    }
                }
            }
        }
    }

    val numberResult = rememberLauncherForActivityResult(contract = ActivityResultContracts.StartIntentSenderForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            try {
                Identity.getSignInClient(context).apply {
                    val tel = getPhoneNumberFromIntent(result.data)
                    tel.split("\\+\\d{2}".toRegex()).let {
                        if (it.size > 1) {
                            model.setNumber(it[1])
                        }
                    }
                }
            } catch (_: Exception) {
            }
        }
    }

    DisposableEffect(model) {
        DamiApplication.topBar(Color.Transparent)
        DamiLoginCode.onCall = { intent ->
            codeResult.launch(intent)
        }
        try {
            Identity.getSignInClient(context)
                .getPhoneNumberHintIntent(GetPhoneNumberHintIntentRequest.builder().build())
                .addOnSuccessListener { intent ->
                    try {
                        numberResult.launch(IntentSenderRequest.Builder(intent).build())
                    } catch (_: Exception) {
                    }
                }
        } catch (_: Exception) {
        }
        onDispose {
            DamiApplication.topBar(Color(0xff016b76))
            DamiLoginCode.onCall = null
        }
    }

    BackHandler { model.clickBack(controller) }

    DamiView(model, containerColor = Color.Transparent, onBack = { model.clickBack(controller) }) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(brush = Brush.verticalGradient(listOf(Color(0xff84c4be), Color.White, Color.White, Color.White)))
                .statusBarsPadding()
                .padding(top = 30.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DamiImage(modifier = Modifier.size(100.dp), id = R.mipmap.logo)
            Text(modifier = Modifier.padding(top = 15.dp), text = "Hey! Welcome", fontSize = 28.sp, lineHeight = 34.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
            Text(text = "Dami Credit", fontSize = 28.sp, lineHeight = 34.sp, color = Color(0xff2e9a6d), fontWeight = FontWeight.Medium)

            AnimatedVisibility(!state.showCode) {
                DamiInput(
                    modifier = Modifier
                        .padding(start = 40.dp, end = 40.dp, top = 30.dp)
                        .background(Color(0xfff2f2f2), shape = CircleShape)
                        .padding(4.dp),
                    value = state.number,
                    onValueChange = { model.setNumber(it) },
                    placeholder = "Cellphone number",
                    keyboardType = KeyboardType.Phone,
                    startView = {
                        Text(
                            modifier = Modifier
                                .background(Color.White, shape = CircleShape)
                                .padding(horizontal = 16.dp, vertical = 10.dp),
                            text = "+63",
                            fontSize = 16.sp,
                            lineHeight = 20.sp,
                            color = Color(0xff2a292a),
                            fontWeight = FontWeight.Medium
                        )
                    }
                )
            }
            AnimatedVisibility(state.showCode) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        modifier = Modifier.padding(start = 40.dp, end = 40.dp, top = 20.dp),
                        text = "Enter the 6-digit verification code",
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        color = Color(0xff2a292a)
                    )
                    DamiCode(
                        modifier = Modifier.padding(start = 40.dp, end = 40.dp, top = 10.dp),
                        value = state.code,
                        onValueChange = { model.setCode(controller, keyboard, it) },
                        focusRequester = codeRequester
                    )
                }
            }
            Text(
                modifier = Modifier
                    .padding(start = 40.dp, end = 40.dp, top = 20.dp)
                    .fillMaxWidth()
                    .background(Color(if ((state.showCode && state.code.length == 6) || (!state.showCode && state.number.length > 8)) 0xff016b76 else 0xffb2c3bd), shape = CircleShape)
                    .clickDelay((state.showCode && state.code.length == 6) || (!state.showCode && state.number.length > 8)) { model.clickBtn(context, controller, keyboard, codeRequester) }
                    .padding(13.dp),
                text = "Sign up / Sign in",
                fontSize = 18.sp,
                lineHeight = 22.sp,
                color = Color.White,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )

            AnimatedVisibility(!state.showCode) {
                Row(modifier = Modifier.padding(start = 40.dp, end = 40.dp, top = 10.dp)) {
                    DamiImage(
                        modifier = Modifier
                            .clip(CircleShape)
                            .clickable { model.agree() }
                            .padding(6.dp)
                            .size(16.dp),
                        id = if (state.agree) R.mipmap.icon_login_select_s else R.mipmap.icon_login_select_n
                    )
                    Text(modifier = Modifier.padding(start = 2.dp, top = 6.dp), text = buildAnnotatedString {
                        append("I have read and agree to the ")
                        withLink(link = LinkAnnotation.Clickable(tag = "", styles = TextLinkStyles(style = SpanStyle(color = Color(0xffffa724), textDecoration = TextDecoration.Underline))) {
                            DamiRouteUtils.navigateWeb(controller, DamiConfigUtils.getPrivacy())
                        }) {
                            append("Privacy Policy")
                        }
                    }, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff6a6a6b))
                }
            }

            AnimatedVisibility(state.showCode) {
                Text(
                    modifier = Modifier
                        .padding(top = 10.dp)
                        .clip(CircleShape)
                        .clickDelay(state.time == 0) { model.loginCode(context, controller, codeRequester) }
                        .padding(6.dp),
                    text = if (state.time == 0) "Resend Code" else "Resend Code(${state.time})",
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    color = Color(if (state.time == 0) 0xfffea824 else 0xffb0b0b0))
            }
        }
    }
}