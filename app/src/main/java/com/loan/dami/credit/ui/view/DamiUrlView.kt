package com.loan.dami.credit.ui.view

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Activity.RESULT_CANCELED
import android.app.Activity.RESULT_OK
import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.JavascriptInterface
import android.webkit.SslErrorHandler
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.FileProvider
import androidx.core.graphics.createBitmap
import androidx.core.net.toUri
import androidx.core.view.WindowCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.android.play.core.review.ReviewManagerFactory
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.api.DamiClient
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiRequestUtils
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.config.DamiUtils.toDamiDecode
import com.loan.dami.credit.config.DamiUtils.toDamiEncode
import com.loan.dami.credit.config.DamiView
import com.loan.dami.credit.ui.route.DamiBankListRoute
import com.loan.dami.credit.ui.route.DamiHomeRoute
import com.loan.dami.credit.ui.route.DamiUrlRoute
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

@SuppressLint("JavascriptInterface", "SetJavaScriptEnabled")
@Composable
fun DamiUrlView(route: DamiUrlRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current
    val scope = rememberCoroutineScope()

    val model = viewModel { DamiModel<DamiState>(DamiState()) }

//    DisposableEffect(model) {
//        val window = (context as Activity).window
//        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
//        window.statusBarColor = Color(0xff016b76).toArgb()
//        window.navigationBarColor = Color.White.toArgb()
//        onDispose {
//            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
//        }
//    }

    var resume by remember { mutableStateOf(false) }
    var title by remember { mutableStateOf("loading...") }
    var progress by remember { mutableIntStateOf(0) }
    var sslErrorHandler by remember { mutableStateOf<SslErrorHandler?>(null) }
    var uploadFiles by remember { mutableStateOf<ValueCallback<Array<Uri?>>?>(null) }
    val file = remember { File("${context.filesDir.path}/photo.jpg") }
    val outUri = remember { FileProvider.getUriForFile(context, "${context.packageName}.fileProvider", file) }

    val result = rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        when (it.resultCode) {
            RESULT_OK -> uploadFiles?.apply {
                onReceiveValue(arrayOf(it.data?.data ?: outUri))
                uploadFiles = null
            }

            RESULT_CANCELED -> uploadFiles?.apply {
                onReceiveValue(null)
                uploadFiles = null
            }
        }
    }

    val chromeClient = remember {
        object : WebChromeClient() {
            override fun onShowFileChooser(webView: WebView?, filePathCallback: ValueCallback<Array<Uri?>>, fileChooserParams: FileChooserParams?): Boolean {
                uploadFiles = filePathCallback
                DamiRequestUtils.requestCamera(context) {
                    when {
                        it -> result.launch(Intent.createChooser(Intent(MediaStore.ACTION_IMAGE_CAPTURE).apply {
                            putExtra(MediaStore.EXTRA_OUTPUT, outUri)
                        }, "File Chooser").apply { putExtra(Intent.EXTRA_INITIAL_INTENTS, arrayOf(Intent(Intent.ACTION_PICK).apply { type = "image/*" })) })

                        else -> uploadFiles?.apply {
                            onReceiveValue(null)
                            uploadFiles = null
                        }
                    }
                }
                return true
            }

            override fun onReceivedTitle(view: WebView?, text: String) {
                title = text
                super.onReceivedTitle(view, text)
            }

            override fun onProgressChanged(p0: WebView?, value: Int) {
                progress = value
                super.onProgressChanged(p0, value)
            }
        }
    }

    val viewClient = remember {
        object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                return url?.let {
                    when {
                        it.startsWith("mailto:") || it.startsWith("tel:") || it.startsWith("sms:") -> try {
                            context.startActivity(Intent(Intent.ACTION_VIEW, url.toUri()))
                            true
                        } catch (_: Exception) {
                            super.shouldOverrideUrlLoading(view, url)
                        }

                        else -> super.shouldOverrideUrlLoading(view, url)
                    }
                } ?: super.shouldOverrideUrlLoading(view, url)
            }

            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                return request?.url?.let {
                    when {
                        it.toString().startsWith("mailto:") || it.toString().startsWith("tel:") || it.toString().startsWith("sms:") -> try {
                            context.startActivity(Intent(Intent.ACTION_VIEW, it))
                            true
                        } catch (_: Exception) {
                            super.shouldOverrideUrlLoading(view, request)
                        }

                        else -> super.shouldOverrideUrlLoading(view, request)
                    }
                } ?: super.shouldOverrideUrlLoading(view, request)
            }

            override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
                sslErrorHandler = handler
            }
        }
    }


    val webView = remember {
        WebView(context).apply {
            layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            setInitialScale(100)
            setLayerType(View.LAYER_TYPE_HARDWARE, null)
            settings.apply {
                loadWithOverviewMode = false
                saveFormData = true
                setSupportZoom(true)
                builtInZoomControls = true
                displayZoomControls = false
                cacheMode = WebSettings.LOAD_DEFAULT
                useWideViewPort = true
                javaScriptEnabled = true
                blockNetworkImage = false
                domStorageEnabled = true
                layoutAlgorithm = WebSettings.LayoutAlgorithm.NARROW_COLUMNS
                setSupportMultipleWindows(true)
                mixedContentMode = WebSettings.LOAD_NORMAL
                textZoom = 100
                javaScriptCanOpenWindowsAutomatically = true
            }
            webChromeClient = chromeClient
            webViewClient = viewClient
        }
    }

    val callback = remember {
        object : Any() {
            @JavascriptInterface
            fun finish() {
                scope.launch { controller.popBackStack(route, true) }
            }

            @JavascriptInterface
            fun home() {
                scope.launch { controller.popBackStack(DamiHomeRoute, false) }
            }

            @JavascriptInterface
            fun decode(str: String) = str.toDamiDecode()

            @JavascriptInterface
            fun encode(str: String) = str.toDamiEncode()

            @JavascriptInterface
            fun header(path: String, time: Long) = DamiClient.header(path, time).toString().toDamiEncode()

            @JavascriptInterface
            fun navigate(url: String, close: Int) {
                scope.launch {
                    model.damiNavigate(context, controller, url, msg = "url error") {
                        if (close == 1) controller.popBackStack(route, true)
                    }
                }
            }

            @JavascriptInterface
            fun setTitle(text: String) {
                scope.launch { title = text }
            }

            @JavascriptInterface
            fun retryData(orderNo: String) {
                model.retryData(controller, orderNo) { webView.reload() }
            }

            @JavascriptInterface
            fun navigateBankList(id: String, orderNo: String) {
                scope.launch { controller.navigate(DamiBankListRoute(id, orderNo)) }
            }

            @JavascriptInterface
            fun vending() {
                try {
                    context.packageManager.getPackageInfo("com.android.vending", 0)
                    scope.launch {
                        ReviewManagerFactory.create(context).apply {
                            requestReviewFlow().addOnCompleteListener {
                                if (it.isSuccessful) {
                                    try {
                                        launchReviewFlow(context as Activity, it.result).addOnCompleteListener { }
                                    } catch (_: Exception) {
                                    }
                                }
                            }
                        }
                    }
                } catch (_: Exception) {
                }
            }

            @JavascriptInterface
            fun showLoading() {
                model.showLoading()
            }

            @JavascriptInterface
            fun closeLoading() {
                model.closeLoading()
            }

            @JavascriptInterface
            fun qrCode() {
                scope.launch(Dispatchers.IO) {
                    showLoading()
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        val bitmap = createBitmap(webView.width, webView.height).apply {
                            val c = Canvas(this)
                            webView.draw(c)
                        }
                        context.contentResolver.apply {
                            insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, ContentValues().apply {
                                put(MediaStore.Images.Media.DISPLAY_NAME, "photo_${System.currentTimeMillis()}.png")
                                put(MediaStore.Images.Media.MIME_TYPE, "image/png")
                                put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                            })?.let { uri ->
                                try {
                                    openOutputStream(uri)?.use { output ->
                                        bitmap.compress(Bitmap.CompressFormat.PNG, 100, output)
                                        closeLoading()
                                        model.toast("Saved to album.")
                                    }
                                } catch (_: Exception) {
                                    closeLoading()
                                    model.toast("Your phone's OS is outdated, so the QR code can't be saved automatically. Please take a screenshot to use it.")
                                }
                            }
                        }
                    } else {
                        closeLoading()
                        model.toast("Your phone's OS is outdated, so the QR code can't be saved automatically. Please take a screenshot to use it.")
                    }
                }
            }
        }
    }

    DisposableEffect(Unit) {
        if (resume) {
            resume = false
            webView.reload()
        }
        onDispose { resume = true }
    }

    val back = {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            controller.popBackStack(route, true)
        }
    }

    BackHandler { back() }

    DamiView(model, title = title, onBack = { back() }) {
        Box(modifier = Modifier.padding(it)) {
            AndroidView(
                modifier = Modifier.fillMaxSize(),
                factory = { webView.apply { addJavascriptInterface(callback, "DamiCredit") } },
                update = { it.loadUrl(route.url) },
                onReset = {
                    it.onResume()
                    it.resumeTimers()
                },
                onRelease = {
                    it.apply {
                        loadDataWithBaseURL(null, "", "text/html", "utf-8", null)
                        stopLoading()
                        webChromeClient = null
                        visibility = View.GONE
                        removeAllViews()
                        destroy()
                    }
                    try {
                        Class.forName("android.webkit.BrowserFrame").getDeclaredField("sConfigCallback")?.apply {
                            isAccessible = true
                            set(null, null)
                        }
                    } catch (_: Exception) {
                    }
                }
            )
            if (progress < 100) {
                LinearProgressIndicator(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(2.dp),
                    progress = { progress.toFloat() / 100 },
                    color = Color.Blue,
                    trackColor = Color.White,
                    gapSize = 0.dp,
                    drawStopIndicator = {}
                )
            }
        }
    }
}