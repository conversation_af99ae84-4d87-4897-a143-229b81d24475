package com.loan.dami.credit.config


import android.annotation.SuppressLint
import android.content.Context
import android.location.Address
import android.location.Geocoder
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Build
import android.os.Bundle
import android.os.Looper
import com.loan.dami.credit.config.DamiUtils.buildDami
import com.loan.dami.credit.config.DamiUtils.toDamiAes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import java.util.Locale
import kotlin.coroutines.resume

object DamiLocationUtils {
    var lng = ""
    var lat = ""
    var gpsInfo = buildJsonObject { }

    private var locationData: String? = null

    suspend fun location(context: Context) {
        if (DamiRequestUtils.locationEnable(context) && DamiRequestUtils.grantedLocation(context)) {
            locationData = getLocationData(context)
        }
    }

    suspend fun getLocationData(context: Context): String? {
        return locationData ?: withTimeoutOrNull(6000L) {
            reqLocation(context)?.let {
                geocoderLocation(context, it)
            }
        }
    }

    @SuppressLint("MissingPermission")
    private suspend fun reqLocation(context: Context): Location? = suspendCancellableCoroutine { coroutine ->
        val lm = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

        val listener = object : LocationListener {
            override fun onLocationChanged(location: Location) {
                lm.removeUpdates(this)
                if (coroutine.isActive) {
                    coroutine.resume(location)
                } else {
                    MainScope().launch(Dispatchers.IO) {
                        locationData = geocoderLocation(context, location)
                    }
                }
            }

            override fun onProviderDisabled(provider: String) {}

            override fun onProviderEnabled(provider: String) {}

            override fun onFlushComplete(requestCode: Int) {}

            override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {}
        }
        try {
            lm.requestLocationUpdates(LocationManager.NETWORK_PROVIDER, 6000L, 1000F, listener, Looper.getMainLooper())
        } catch (_: Exception) {
        }
        MainScope().launch(Dispatchers.IO) {
            delay(3000)
            val location = getLastKnownLocation(lm)
            if (coroutine.isActive) {
                coroutine.resume(location)
            }
        }

        coroutine.invokeOnCancellation { }
    }

    @SuppressLint("MissingPermission")
    private fun getLastKnownLocation(lm: LocationManager): Location? {
        try {
            lm.getLastKnownLocation(LocationManager.GPS_PROVIDER)?.let {
                return it
            }
        } catch (_: Exception) {
        }
        try {
            lm.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)?.let {
                return it
            }
        } catch (_: Exception) {
        }
        try {
            lm.getLastKnownLocation(LocationManager.PASSIVE_PROVIDER)?.let {
                return it
            }
        } catch (_: Exception) {
        }
        return null
    }

    private suspend fun geocoderLocation(context: Context, location: Location): String? = suspendCancellableCoroutine { coroutine ->
        try {
            lat = location.latitude.toString()
            lng = location.longitude.toString()
            Geocoder(context, Locale.getDefault()).apply {
                when {
                    Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                        getFromLocation(location.latitude, location.longitude, 1) {
                            if (coroutine.isActive) {
                                coroutine.resume(it.toLocation())
                            }
                        }
                    }

                    else -> {
                        getFromLocation(location.latitude, location.longitude, 1)?.let {
                            if (coroutine.isActive) {
                                coroutine.resume(it.toLocation())
                            }
                        }
                    }
                }
            }
        } catch (_: Exception) {
            if (coroutine.isActive) {
                coroutine.resume(null)
            }
        }
        coroutine.invokeOnCancellation {}
    }

    private fun List<Address>.toLocation() = buildDami {
        try {
            when {
                isNotEmpty() -> first().also { addr ->
                    lat = addr.latitude.toString()
                    lng = addr.longitude.toString()
                    put("styta", addr.longitude.toString())
                    put("ectdial", addr.latitude.toString())
                    put("eresph", addr.getAddressLine(0))
                    put("hergrandfat", addr.countryName)
                    put("arpsh", addr.countryCode)
                    put("ipesw", addr.adminArea)
                    put("diostu", addr.locality)
                    put("ardforw", addr.subAdminArea)
                    put("actcomp", addr.featureName)
                    gpsInfo = buildJsonObject {
                        put("getfor", addr.longitude.toString())
                        put("cayde", addr.latitude.toString())
                        put("antdomin", addr.getAddressLine(0))
                        put("tedrela", buildJsonObject {
                            put("hergrandfat", addr.countryName)
                            put("arpsh", addr.countryCode)
                            put("ipesw", addr.adminArea)
                            put("diostu", addr.locality)
                            put("ardforw", addr.subAdminArea)
                            put("actcomp", addr.featureName)
                        })
                    }
                }

                else -> {
                    put("styta", lng)
                    put("ectdial", lat)
                }
            }
        } catch (_: Exception) {
            put("styta", lng)
            put("ectdial", lat)
        }
    }.toDamiAes()

}