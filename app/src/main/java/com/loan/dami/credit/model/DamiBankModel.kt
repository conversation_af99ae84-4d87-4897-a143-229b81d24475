package com.loan.dami.credit.model

import android.content.Context
import androidx.navigation.NavHostController
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiBankBean
import com.loan.dami.credit.bean.DamiPersonalBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiBankRoute
import kotlinx.serialization.json.put

data class DamiBankState(val list: List<DamiPersonalBean>? = null, var autoState: Int = 0) : DamiState()

class DamiBankModel(private val route: DamiBankRoute) : DamiModel<DamiBankState>(DamiBankState()) {

    fun bankData(controller: NavHostController) {
        emitData<DamiPersonalBean>(controller, request = { DamiApi.bankData(route.id) }) {
            setData(data.copy(list = it.items))
        }
    }

    fun saveBankData(context: Context, controller: NavHostController, indexState: Int) {
        showLoading()
        emitData<DamiBankBean>(controller, request = {
            DamiApi.saveBankData {
                put("eamcr", route.id)
                put("ishpubl", indexState + 1)
                put("ockst", "ishpubl")
                data.list?.get(indexState)?.items?.forEach { put(it.code, it.getText()) }
            }
        }, loading = false, complete = { if (!it) closeLoading() }) {
            if (route.add) {
                bankCheckData(context, controller, route.orderNo, it.bindId, route.type)
            } else {
                damiData(8, route.id, route.orderNo) {
                    damiVerify(context, controller, route.id) { controller.popBackStack() }
                }
            }
        }
    }

    fun setAutoState(auto: Int) {
        setData(data.copy(autoState = auto))
    }
}