package com.loan.dami.credit.config

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.json.JsonNamingStrategy

@OptIn(ExperimentalSerializationApi::class)
class DamiStrategy : JsonNamingStrategy {

    private val mapName = mapOf(
        "phone" to "laxearthf",
        "EQUINOX" to "ortdep",
        "code" to "urelect",
        "message" to "ateperfor",
        "data" to "oryfact",
        "ZEPHYR" to "acemispl",
        "username" to "iveeffect",
        "smsCode" to "ertdes",
        "LUMINARY" to "ionintent",
        "SERENDIPITY" to "ceroffi",
        "isOld" to "anaban",
        "smsMaxId" to "intconstra",
        "realname" to "oselo",
        "sessionId" to "atediscrimin",
        "QUAGMIRE" to "inediscipl",
        "EXUBERANT" to "urepremat",
        "JUBILANT" to "iffsn",
        "MYSTIQUE" to "ketjac",
        "EUPHORIA" to "ralmo",
        "icon" to "tordemonstra",
        "iconUrl" to "ateelabor",
        "linkUrl" to "nceaudie",
        "list" to "thmrhy",
        "type" to "antten",
        "item" to "sonsalesper",
        "id" to "hipcensors",
        "url" to "anddem",
        "imgUrl" to "delmo",
        "productName" to "glewrig",
        "productLogo" to "ashsm",
        "buttonText" to "bleincredi",
        "amountRange" to "uremeas",
        "amountRangeDes" to "nceexperie",
        "termInfo" to "iverepresentat",
        "termInfoDes" to "ionreproduct",
        "loanRate" to "ectresp",
        "loanRateDes" to "omyecon",
        "loan_process_text" to "putout",
        "certify_finished" to "rioscena",
        "loan_process_list" to "deovi",
        "title" to "entimprovem",
        "loan_amount" to "rgeenla",
        "selected" to "ingbr",
        "period_list" to "akeearthqu",
        "period" to "ionvers",
        "periodDesc" to "ionmutat",
        "termInfoImg" to "rchma",
        "loanRateImg" to "diora",
        "order_no" to "undref",
        "product_id" to "eamcr",
        "amount" to "larsecu",
        "amount_text" to "elyunlik",
        "date" to "eummus",
        "date_text" to "nceaccepta",
        "account" to "ilemob",
        "account_text" to "nchbra",
        "order_status" to "ineunderl",
        "order_status_text" to "ioncalculat",
        "first_failed_at" to "earcl",
        "cancel_end_at" to "ioncontradict",
        "buttons" to "uredepart",
        "ifShow" to "ionpercept",
        "button_text" to "ustadj",
        "productTags" to "tchscra",
        "productDesc" to "werans",
        "product_code" to "ionadministrat",
        "buttoncolor" to "methel",
        "buttonStatus" to "ioninvestigat",
        "buttonExplain" to "ousjeal",
        "inside" to "aseincre",
        "term" to "allst",
        "productType" to "eckch",
        "isCopyPhone" to "astbroadc",
        "loan_rate" to "entpavem",
        "todayClicked" to "ingastonish",
        "labelText" to "kinpump",
        "titleText" to "rtypa",
        "sordDesc" to "eelwh",
        "todayApplyNum" to "torrefrigera",
        "amountMax" to "hopbis",
        "loanTermText" to "orylaborat",
        "RENDEZVOUS" to "mitli",
        "signInUrl" to "tenrot",
        "waitBindCard" to "thebrea",
        "waitCashWithdrawal" to "ealcer",
        "waitRepayment" to "iumsod",
        "ifRedPoint" to "naloperatio",
        "redPointId" to "ornstubb",
        "is_newbie" to "ongwr",
        "mail" to "etyvari",
        "key" to "teren",
        "tips" to "terthea",
        "is_h5" to "ankbl",
        "native_key" to "ingfeel",
        "sort" to "ileag",
        "style" to "ioninvas",
        "is_display_sign" to "essdr",
        "sign_type" to "ifekn",
        "status" to "astfe",
        "created_at" to "ionrecept",
        "updated_at" to "alkcrossw",
        "not_read_num" to "orymem",
        "extendsList1" to "ionsess",
        "extendList2" to "sorprofes",
        "sign_in" to "letbul",
        "broadcast_news" to "orest",
        "extendLists" to "optad",
        "tag" to "ionexpans",
        "log" to "ngestra",
        "VORTEX" to "ioncombinat",
        "ZENITH" to "donaban",
        "result" to "ulemod",
        "adaptation_page" to "orefolkl",
        "dialog" to "ionconvict",
        "app_version_max" to "nceappeara",
        "dialog_content" to "ticdemocra",
        "pre_membership_level" to "stewa",
        "level" to "vascan",
        "name" to "aicmos",
        "logo" to "ivetalkat",
        "cur_membership_level" to "ishpol",
        "cur_benefits" to "ialmater",
        "content" to "ncegla",
        "img_url" to "ghtfrei",
        "banner_config_id" to "obewardr",
        "BROKER" to "lowpil",
        "module_id" to "tercen",
        "position" to "rcereinfo",
        "position_id" to "iontract",
        "source" to "ousconsci",
        "MAJESTIC" to "gletrian",
        "ECLIPSE" to "bletrou",
        "max_amount" to "lueva",
        "min_amount" to "ionpopulat",
        "terms" to "eedbr",
        "term_type" to "ghtti",
        "loan_mode" to "ierbarr",
        "msg" to "atedeterior",
        "accessKey" to "sinba",
        "secretKey" to "ferof",
        "QUIXOTIC" to "caphandi",
        "LUCIDITY" to "ifyclass",
        "ETHEREAL" to "intdisappo",
        "productDetail" to "lthwea",
        "amountArr" to "tlecas",
        "termArr" to "idebr",
        "amountDesc" to "ncebou",
        "termDesc" to "clevehi",
        "orderNo" to "eadthr",
        "orderId" to "ionviolat",
        "columnText" to "ialjudic",
        "tag1" to "ionrestorat",
        "text" to "bowrain",
        "tag2" to "cleun",
        "buttonUrl" to "nceinterfere",
        "hotline" to "indbl",
        "value" to "perpa",
        "complaintUrl" to "ultdefa",
        "useEpoch" to "ityquant",
        "userInfo" to "ateappreci",
        "idNumber" to "rusvi",
        "verify" to "ifyintens",
        "subtitle" to "ryoemb",
        "statusName" to "odeexpl",
        "taskType" to "oolsch",
        "canClick" to "tedtalen",
        "optional" to "terquar",
        "ifMust" to "zonhori",
        "canClickMessage" to "nkstha",
        "nextStep" to "eldbattlefi",
        "step" to "hedestablis",
        "agreement" to "ureagricult",
        "productId" to "lsefa",
        "scene" to "assgr",
        "INFINITUM" to "itycommun",
        "pan" to "veysur",
        "id_front" to "ortres",
        "info" to "oolst",
        "id_number" to "reerefe",
        "birthday" to "ineunderm",
        "id_back" to "undcompo",
        "cardTypeList" to "essstr",
        "cardTypeList2" to "terclus",
        "recommended" to "entperc",
        "card" to "achappro",
        "demonstration_url" to "eercar",
        "wrong_demonstration_url" to "iseno",
        "others" to "iondirect",
        "image_source" to "ngeora",
        "cardType" to "ishpubl",
        "livenessId" to "tryfores",
        "faceType" to "monle",
        "gender" to "entdisappointm",
        "year" to "iesser",
        "month" to "ialtr",
        "day" to "arytempor",
        "confidence" to "eekch",
        "EPHEMERAL" to "erypott",
        "RESPLENDENT" to "larcel",
        "LUMINOUS" to "entattachm",
        "definition" to "sincou",
        "statements" to "ersleftov",
        "result_code" to "arnye",
        "biz_url" to "ureexpendit",
        "biz_token" to "rvese",
        "error" to "nceadva",
        "SERAPHIC" to "ingfl",
        "items" to "ismmechan",
        "cate" to "icenot",
        "inputType" to "enddef",
        "note" to "urestruct",
        "enable" to "ionprotect",
        "dateSelect" to "urepleas",
        "ELEVATION" to "umecost",
        "TRANQUILITY" to "derten",
        "education" to "sitvi",
        "home_address" to "ineimag",
        "home_city" to "allbaseb",
        "home_phone" to "tergut",
        "live" to "anovolc",
        "marriage" to "ketbas",
        "purpose" to "ndeblo",
        "sex" to "ughdo",
        "company_address" to "ngera",
        "company_name" to "rchresea",
        "company_phone" to "pleap",
        "job_industry" to "sesglas",
        "job_type" to "keyjoc",
        "payday" to "herot",
        "salary" to "ncedisturba",
        "work_city" to "letbal",
        "work_length" to "enddep",
        "MYSTERIOUS" to "ghtthou",
        "JUBILATION" to "urepast",
        "ZEPPELIN" to "redti",
        "EUPHORIC" to "ncebrillia",
        "emergent" to "amebl",
        "relation" to "iseprom",
        "mobile" to "odyblo",
        "number1" to "herfeat",
        "dropdown" to "terghostwri",
        "alternate_phone" to "torreac",
        "EXHILARATION" to "icecrev",
        "EQUANIMITY" to "agediscour",
        "VIVACIOUS" to "opehorosc",
        "displayValue" to "iftdr",
        "showOptionalList" to "terut",
        "channelCode" to "utemin",
        "firstName" to "ranvete",
        "middleName" to "bitrab",
        "lastName" to "terpain",
        "cardNo" to "acyliter",
        "confirmCardNo" to "verobser",
        "RADIANT" to "ockst",
        "bindId" to "itypossibil",
        "dictProvinceList" to "antunpleas",
        "provinceCode" to "agehost",
        "LIMPIDITY" to "nalconventio",
        "ZANY" to "nchlu",
        "cardTypeName" to "nchra",
        "cardTypeIcon" to "gerteena",
        "bankName" to "teeguaran",
        "option" to "gleea",
        "isMain" to "lotbal",
        "RADIANCE" to "zeechimpan",
        "redirectUrl" to "ateclim",
        "EMBRACEABLE" to "uregest",
        "noteText" to "ionreflect",
        "text1" to "ionrecess",
        "text2" to "achstom",
        "LAVISH" to "nelpa",
        "NOSTALGIA" to "serla",
        "FERVOR" to "dalscan",
        "INEFFABLE" to "akesh",
        "orderType" to "ignres",
        "pageNum" to "athde",
        "pageSize" to "oveimpr",
        "orderStatus" to "berbom",
        "noticeText" to "ialimper",
        "orderStatusDesc" to "iceprejud",
        "orderAmount" to "iteinfin",
        "loanDetailUrl" to "itydign",
        "dateText" to "ateestim",
        "moneyText" to "dgeple",
        "dateValue" to "estch",
        "overdueDays" to "fetbuf",
        "isLoan" to "ityresponsibil",
        "loanTime" to "ngelou",
        "repayTime" to "psecor",
        "pages" to "ogytechnol",
        "market" to "dixappen",
        "device_info" to "icesacrif",
        "extinfo" to "ectobj",
        "initEvent" to "ainpl",
        "afKey" to "nalconstitutio",
        "adjust_token" to "gerdan",
        "bettary" to "ionpermiss",
        "brand" to "mptexe",
        "carrier" to "okerev",
        "imei" to "ageman",
        "ip" to "derconsi",
        "is_root" to "agemiscarri",
        "is_simulator" to "itycredibil",
        "mac" to "ushfl",
        "memory" to "eelst",
        "os_type" to "entag",
        "os_version" to "clerecy",
        "pic_count" to "emeextr",
        "resolution" to "ageim",
        "sdcard" to "agegarb",
        "storage" to "entenvironm",
        "unuse_sdcard" to "eldfi",
        "unuse_storage" to "cedexperien",
        "wifi" to "uceprod",
        "wifi_name" to "arysupplement",
        "sceneType" to "aryannivers",
        "deviceNo" to "fulgrate",
        "equipmentB" to "asyfant",
        "longitude" to "styta",
        "latitude" to "ectdial",
        "startTime" to "ucered",
        "endTime" to "iumstad",
        "last_login_time" to "essweakn",
        "audio_external" to "allfootb",
        "audio_internal" to "rshma",
        "albs" to "ealrev",
        "package_name" to "iveattract",
        "battery_status" to "pleprinci",
        "battery_level" to "ecteff",
        "battery_max" to "fulbeauti",
        "battery_pct" to "arysumm",
        "is_ac_charge" to "fulaw",
        "is_charging" to "isesurpr",
        "is_usb_charge" to "norho",
        "gps_info" to "ncecomplia",
        "gps_longitude" to "getfor",
        "gps_latitude" to "cayde",
        "gps_address" to "antdomin",
        "address_info" to "tedrela",
        "country_name" to "hergrandfat",
        "country_code" to "arpsh",
        "admin_area" to "ipesw",
        "locality" to "diostu",
        "sub_admin_area" to "ardforw",
        "feature_name" to "actcomp",
        "general_data" to "ateevalu",
        "idfv" to "antsl",
        "idfa" to "inefemin",
        "and_id" to "onycol",
        "gaid" to "nceda",
        "currentSystemTime" to "ualrit",
        "elapsedRealtime" to "ndsgrou",
        "is_usb_debug" to "acepl",
        "is_using_proxy_port" to "blefavora",
        "is_using_vpn" to "verho",
        "language" to "ionrecommendat",
        "locale_display_language" to "ttecasse",
        "locale_iso_3_country" to "rrywo",
        "locale_iso_3_language" to "rumfo",
        "network_operator_name" to "akesn",
        "network_type" to "anteleph",
        "sensor_list" to "tersis",
        "maxRange" to "gbyru",
        "minDelay" to "antarrog",
        "power" to "atehesit",
        "vendor" to "allbasketb",
        "version" to "tlebat",
        "time_zone_id" to "urefig",
        "uptimeMillis" to "hesclot",
        "hardware" to "ellsp",
        "board" to "iondefinit",
        "cores" to "eerpion",
        "device_height" to "terfil",
        "device_name" to "ioninhibit",
        "device_width" to "ioncommiss",
        "model" to "ionconsiderat",
        "physical_size" to "blesta",
        "production_date" to "ainbarg",
        "release" to "sayes",
        "sdk_version" to "percop",
        "serial_number" to "ainsust",
        "network" to "eseche",
        "configured_wifi" to "vetvel",
        "bssid" to "uremat",
        "ssid" to "manfisher",
        "current_wifi" to "terwa",
        "wifi_count" to "vershi",
        "ram_total_size" to "atenomin",
        "ram_usable_size" to "urelegislat",
        "memory_card_size" to "ormst",
        "memory_card_usable_size" to "ngearra",
        "memory_card_size_use" to "ionexempt",
        "internal_storage_total" to "undwo",
        "internal_storage_usable" to "nchpu",
        "contain_sd" to "eepcr",
        "extra_sd" to "eammainstr",
        "address" to "eresph",
        "times_contacted" to "ownbreakd",
        "last_time_contacted" to "andgr",
        "up_time" to "onyir",
        "last_time_used" to "roweyeb",
        "group" to "setas",
        "appName" to "rermanufactu",
        "packageName" to "ageencour",
        "versionCode" to "lyxepica",
        "versionName" to "igeprest",
        "inTime" to "ishrubb",
        "upTime" to "fertrans",
        "flags" to "ortcomf",
        "appType" to "nerdin",
        "person" to "ionexplos",
        "body" to "ioncreat",
        "date_sent" to "noxequi",
        "read" to "entmom",
        "subject" to "manfresh",
        "seen" to "elfsh",
        "description" to "dlyfrien",
        "end_time" to "ionprofess",
        "event_id" to "etyanxi",
        "event_title" to "uchto",
        "reminders" to "rolcont",
        "eventId" to "etecompl",
        "method" to "ateunfortun",
        "minutes" to "oangr",
        "reminder_id" to "sisanaly",
        "start_time" to "elylik",
        "fcm_token" to "airaff",
        "tax_card" to "omesyndr",
        "id_card_back" to "ortdist",
        "face" to "derlad",
        "family_monthly_salary" to "ovegl",
        "residentaddress" to "rorhor",
        "home_pin_code" to "utedil",
        "email" to "eshfl",
        "spouse_name" to "estlat",
        "children_num" to "acyconspir",
        "pay_method" to "perpros",
        "work_industry" to "orknetw",
        "company_full_address" to "isera",
        "company_pincode" to "allsm",
        "monthly_income" to "tchpa",
        "salary_day" to "werflo",
        "salary_type" to "udyst",
        "postalcode" to "nceconscie",
        "alternate_mobile" to "iveposit",
        "actual_period_type" to "ontconfr",
        "actual_amount" to "ionconfrontat",
        "displayAmount" to "ionprovis",
        "displayTerm" to "entjudgm",
        "bankCard" to "ketpac",
        "loan_agreement_url" to "eadspr",
        "serviceFee" to "ionadopt",
        "displayServiceFee" to "terwai",
        "serviceFeeDetail" to "terdaugh",
        "countdownModule" to "nsenonse",
        "time" to "yorma",
        "percent" to "ectinf",
        "loan_periods" to "ionfract",
        "optional_period_type" to "atest",
        "information" to "ronelect",
        "repay_plans" to "fulcolor",
        "period_text" to "aryordin",
        "pay_time" to "utedistrib",
        "repay_money" to "ncepri",
        "repay_principal" to "boncar",
        "repay_interest" to "atecre",
        "available_amounts" to "ainfount",
        "periods" to "endext",
        "termUnit" to "actextr",
        "lendDetailsUrl" to "tenfrigh",
        "user_id" to "ncyemerge",
        "fail_reason" to "uryinj",
        "frozen_interval" to "essconsciousn",
        "order_from" to "entstatem",
        "bind_card" to "ionomiss",
        "loan_confirm" to "ormtransf",
        "basic_push_time" to "zendo",
        "extra_push_time" to "ncebala",
        "completed" to "ionexcavat",
        "create_time" to "istwr",
        "update_time" to "aceneckl",
        "client_type" to "rstfi",
        "user_from" to "urefeat",
        "app_from" to "emyen",
        "is_new" to "getbud",
        "if_confirm_loan" to "dercylin",
        "installment_info" to "iseexerc",
        "cash_tag" to "terca",
        "audit_time" to "unefort",
        "loan_time" to "ionquest",
        "shopping_mode" to "ateinfl",
        "order_type" to "ardstand",
        "is_reloan" to "essbusin",
        "product_new_user" to "zenfro",
        "product_reloan" to "ickth",
        "first_extra_push_time" to "orbabs",
        "route_code" to "itepol",
        "success_basic_push_time" to "itesatell",
        "desc" to "ialtriv",
        "LUMINESCENT" to "ionchamp",
        "noticeStatusText" to "ortsupp",
        "noticeDesText" to "apescr",
        "v_code" to "essprogr",
        "telephone" to "ghtfli",
        "LoanAmountAsExpect" to "manpolice",
        "nextLoanAmountAsExpect" to "ionorientat",
        "nextNextLoanAmountAsExpect" to "untdisco",
        "isDelay" to "tordistribu",
        "recProducts" to "iceadv",
        "isStoreWithdraw" to "agecour",
        "userOrderDetail" to "nutpea",
        "detail" to "estprot",
        "isScore" to "germana",
        "h5Url" to "ialpotent",
        "sysCancel" to "nalfunctio",
        "cancelText" to "ineguidel",
        "loanFailed" to "ketbuc",
        "retryCard" to "ernpatt",
        "changeCard" to "ivenegat",
        "loanStatus" to "undso",
        "should_display_easter_egg" to "ionconvent",
        "easter_egg_info" to "idedec",
        "sub_title" to "beyab",
        "expire_time" to "ionqualificat",
        "dist_products" to "iongenerat",
        "product_name" to "esssickn",
        "product_logo" to "laxre",
        "loan_terms" to "fulthought",
        "loan_terms_text" to "ngeexcha",
        "interest_rate" to "ttype",
        "interest_rate_text" to "ionconsultat",
        "button_status" to "ackfeedb",
        "total_payment_info" to "mersum",
        "product_info" to "ingtrain",
        "payment_amount_info" to "ityun",
        "payment_amount" to "ainstr",
        "ewallet" to "iontradit",
        "bank" to "entext",
        "overcounter" to "eengr",
        "qr" to "hopworks",
        "transfer" to "ialbur",
        "moduleId" to "erydiscov",
        "positionId" to "uneimm",
        "MYRIAD" to "endleg",
        "RESILIENCE" to "terlet",
        "score" to "iveexecut",
        "resultCode" to "alkch",
        "errMessage" to "iorjun",
        "noticeMessage" to "istdent",
        "titleMessage" to "versil",
        "repaymentDate" to "essreckl",
        "LACONIC" to "eelkn",
        "displayTotalPayment" to "lveso",
        "totalPaymentText" to "iteel",
        "repaymentSchedules" to "ionedit",
        "periodNo" to "nceinflue",
        "dueTime" to "inemagaz",
        "paymentAmount" to "essfitn",
        "displayPaymentAmount" to "thyapa",
        "periodStatus" to "ltycrue",
        "statusText" to "ectprot",
        "repayDes" to "amadr",
        "overdueDay" to "ioncorrect",
        "overdueDayDes" to "taldigi",
        "overdueFee" to "ockfl",
        "loanPrincipal" to "emeth",
        "repaidAmount" to "dlebun",
        "repayInterest" to "itesp",
        "isFold" to "vorsurvi",
        "typecode" to "allwaterf",
        "PARKBENCH" to "iceserv",
        "repayUrl" to "ginbe",
        "isToJump" to "ellsh",
        "upperNote" to "aittr",
        "expiredTime" to "ionradiat",
        "expiredTimeText" to "erycemet",
        "channelLogo" to "bleno",
        "channelName" to "ailsn",
        "paymentCode" to "bleaccepta",
        "paymentNote" to "urenat",
        "details" to "nueave",
        "barcode" to "ncerelia",
        "repayGuidImg" to "ectrefl",
        "repayType" to "ionorganisat",
        "qrUrl" to "arybenefici",
        "qrData" to "untha",
        "PREVENT" to "retreg",
        "loanTerms" to "acksn",
        "loanTermsText" to "acetr",
        "interestRate" to "ioncommunicat",
        "interestRateText" to "azegr",
        "BESIDES" to "tercoun",
        "principal" to "tenstraigh",
        "shelf_product" to "agevoy",
        "real_term" to "tchtwi",
        "first_period_pay_time" to "mancrafts",
        "daily_interest_rate" to "iveconce",
        "month_interest_rate" to "terimpos",
        "daily_interest_amount" to "ionauct",
        "service_rate" to "ectcoll",
        "service_fee" to "ioncompetit",
        "day_overdue_rate" to "rryca",
        "apr" to "audappl",
        "period_no" to "telpas",
        "repay_day" to "iteign",
        "va" to "ententitlem",
        "ELOQUENT" to "ateexcav",
        "problemStatus" to "aterot",
        "problemType" to "oryvict",
        "orderDetail" to "estharv",
        "isShow" to "ttecigare",
        "orderList" to "tchca",
        "NOCTURNAL" to "oteprom",
        "problemSonType" to "rchmona",
        "problemDetail" to "clemira",
        "problemImage" to "atecooper",
        "SERENITY" to "matfor",
        "problemId" to "eonsurg",
        "tabs" to "ncyconstitue",
        "statusDes" to "idegl",
        "shouldReFeedback" to "urefut",
        "AMBIVALENT" to "blevisi",
        "images" to "iveproduct",
        "isSolve" to "ecepi",
        "appraiseScore" to "ingcross",
        "isFeedbackExpired" to "artdep",
        "disposeDetail" to "rgecha",
        "disposeImages" to "oryhist",
        "feedbackList" to "lonco",
        "actionType" to "entinnoc",
        "pid" to "ortsh",
        "question" to "donpar",
        "answerList" to "lumcurricu",
        "title_local_alias" to "isesunr",
        "solve" to "ilyfam",
        "ZESTFUL" to "olibrocc",
        "qaId" to "icselectron",
        "EXHILARATING" to "ampst",
        "clientType" to "gthlen",
        "appVersion" to "sedclo",
        "deviceName" to "ickjoyst",
        "deviceId" to "bleincapa",
        "osVersion" to "egeprivil",
        "appMarket" to "ntyuncertai",
        "gps_adid" to "calcriti",
        "signature" to "aincompl",
        "timestamp" to "ingsw",
        "path" to "ailblackm",
        "getEncryptData" to "ageshort",
        "postEncryptData" to "ashcr",
        "jsonEncryptData" to "ionisolat",
        "tab" to "ioncondit"
    )

    override fun serialNameForJson(descriptor: SerialDescriptor, elementIndex: Int, serialName: String): String {
        return mapName[serialName] ?: serialName
    }

}