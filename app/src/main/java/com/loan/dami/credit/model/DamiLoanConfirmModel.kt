package com.loan.dami.credit.model

import android.content.Context
import androidx.navigation.NavHostController
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiLoanConfirmBean
import com.loan.dami.credit.bean.DamiLoanOtherBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiRequestUtils
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiLoanConfirmRoute

data class DamiLoanConfirmState(val agree: Boolean = true, val bean: DamiLoanConfirmBean? = null, val time: Long = 0, var dialog: DamiLoanOtherBean? = null) : DamiState()

class DamiLoanConfirmModel(private val route: DamiLoanConfirmRoute) : DamiModel<DamiLoanConfirmState>(DamiLoanConfirmState()) {

    fun loanConfirmData(controller: NavHostController, type: Int? = null, amount: String? = null) {
        emitData<DamiLoanConfirmBean>(controller, request = { DamiApi.loanConfirmData(route.id, type, amount) }) {
            setData(data.copy(bean = it, time = it.countdownModule?.time ?: 0))
        }
    }

    fun setTime(time: Long) {
        setData(data.copy(time = time))
    }

    fun loanConfirm(context: Context, controller: NavHostController) {
        if (data.agree) {
            startTime = System.currentTimeMillis()
            DamiRequestUtils.requestLocation(context) {
                if (it) {
                    showLoading()
                    DamiApplication.damiData()
                    emitData<DamiLoanOtherBean>(
                        controller,
                        request = { DamiApi.loanConfirm(route.id, data.bean) },
                        loading = false,
                        complete = { if (!it) closeLoading() }
                    ) { bean ->
                        if (bean.dialog == null) {
                            damiData(10, route.id, data.bean?.orderNo ?: "") {
                                closeLoading()
                                damiNavigate(context, controller, bean.lendDetailsUrl) {
                                    controller.popBackStack()
                                }
                            }
                        } else {
                            closeLoading()
                            setData(data.copy(dialog = bean.dialog))
                        }
                    }
                }
            }
        } else {
            toast("Please read and agree to the privacy policy")
        }
    }

    fun closeDialog(){
        setData(data.copy(dialog = null))
    }

    fun agree() {
        setData(data.copy(agree = !data.agree))
    }
}