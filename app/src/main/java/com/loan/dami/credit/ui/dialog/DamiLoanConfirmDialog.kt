package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.loan.dami.credit.bean.DamiLoanConfirmBean

@Composable
fun DamiLoanConfirmDialog(bean: DamiLoanConfirmBean, onDismiss:()-> Unit) {
    Dialog(onDismissRequest = onDismiss, properties = DialogProperties(usePlatformDefaultWidth = false)) {
        Column(modifier = Modifier.fillMaxSize()) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .clickable(onClick = onDismiss))
            Column(modifier = Modifier.background(color = Color.White, RoundedCornerShape(topEnd = 16.dp, topStart = 16.dp)), horizontalAlignment = Alignment.CenterHorizontally) {
                Text(modifier = Modifier.padding(15.dp), text = "Fee Details", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp), color = Color(0xffe7e7e7))
                Column(Modifier.weight(1f,false).verticalScroll(rememberScrollState())) {
                    Column(
                        modifier = Modifier
                            .padding(10.dp)
                            .border(1.dp, Color(0xff82c3bd), RoundedCornerShape(10.dp))
                    ) {
                        Text(
                            modifier = Modifier
                                .padding(bottom = 5.dp)
                                .fillMaxWidth()
                                .background(Color(0xffdceeed), RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp))
                                .padding(horizontal = 10.dp, vertical = 14.dp),
                            text = "Repayment Plan",
                            fontSize = 16.sp,
                            lineHeight = 20.sp,
                            color = Color(0xff2a292a),
                            fontWeight = FontWeight.Medium
                        )
                        bean.repay_plans?.forEach { item->
                            Column(
                                modifier = Modifier
                                    .padding(horizontal = 10.dp, vertical = 5.dp)
                                    .border(1.dp, Color(0xff016b76), RoundedCornerShape(10.dp))
                                    .padding(horizontal = 10.dp)
                            ) {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    Text(
                                        modifier = Modifier
                                            .background(Color(0xffdceeed), RoundedCornerShape(bottomStart = 10.dp, bottomEnd = 20.dp))
                                            .padding(bottom = 2.dp)
                                            .background(Color(0xff016b76), RoundedCornerShape(bottomStart = 10.dp, bottomEnd = 20.dp))
                                            .padding(horizontal = 15.dp, vertical = 2.dp),
                                        text = "Schedule ${item.period}",
                                        fontSize = 14.sp,
                                        lineHeight = 20.sp,
                                        color = Color.White,
                                        fontWeight = FontWeight.Medium
                                    )
                                    Text(modifier = Modifier.weight(1f), textAlign = TextAlign.End, text = item.pay_time, fontSize = 12.sp, lineHeight = 20.sp, color = Color(0xfff46e00), fontWeight = FontWeight.Medium)
                                }
                                HorizontalDivider(modifier = Modifier.padding(start = 10.dp), color = Color(0xffdceeed))
                                Row(modifier = Modifier.padding(vertical = 4.dp), verticalAlignment = Alignment.CenterVertically) {
                                    Text(modifier = Modifier.weight(1f), text = "Principal", fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xffb0b0b0))
                                    Text(text = item.repay_principal, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
                                }
                                Row(modifier = Modifier.padding(vertical = 4.dp), verticalAlignment = Alignment.CenterVertically) {
                                    Text(modifier = Modifier.weight(1f), text = "Interest", fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xffb0b0b0))
                                    Text(text = item.repay_interest, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
                                }
                            }
                        }
                        Text(modifier = Modifier.padding(start = 10.dp, end = 10.dp, top = 2.dp, bottom = 10.dp), text = bean.note, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xfff46e00), fontWeight = FontWeight.Medium)
                    }
                    bean.serviceFeeDetail?.let { list->
                        Column(
                            modifier = Modifier
                                .padding(start = 10.dp, end = 10.dp, bottom = 10.dp)
                                .border(1.dp, Color(0xff82c3bd), RoundedCornerShape(10.dp))
                                .padding(bottom = 5.dp)
                        ) {
                            Row(modifier = Modifier
                                .padding(bottom = 5.dp)
                                .background(Color(0xffdceeed), RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp))
                                .padding(horizontal = 10.dp, vertical = 14.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(text = "Upfront Service Fee", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                                Text(modifier = Modifier.weight(1f), textAlign = TextAlign.End, text = bean.displayServiceFee, fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xfff46e00), fontWeight = FontWeight.Medium)
                            }
                            list.forEach {
                                Row(modifier = Modifier.padding(horizontal = 10.dp, vertical = 5.dp), verticalAlignment = Alignment.CenterVertically) {
                                    Text(modifier = Modifier.weight(1f), text = it.text, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xffb0b0b0))
                                    Text(text = it.value, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
                                }
                            }
                        }
                    }
                }
                HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp), color = Color(0xffe7e7e7))
                Text(
                    modifier = Modifier
                        .clickable(onClick = onDismiss)
                        .padding(20.dp)
                        .align(Alignment.CenterHorizontally), text = "Cancel", fontSize = 18.sp, lineHeight = 22.sp, color = Color(0xffd1d1d1))
            }
        }
    }
}