package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.sd.lib.compose.wheel_picker.FVerticalWheelPicker
import com.sd.lib.compose.wheel_picker.FWheelPickerFocusVertical
import com.sd.lib.compose.wheel_picker.rememberFWheelPickerState
import java.util.Calendar

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DamiTimeDialog(d: Int, m: Int, y: Int, onDismiss: () -> Unit, onCall: (y: String, m: String, d: String) -> Unit) {
    val date = remember { Calendar.getInstance().apply { set(y, m - 1, d) } }
    val startDate = remember { Calendar.getInstance().apply { set(1900, 1, 1) } }
    val endDate = remember { Calendar.getInstance() }

    val yearCurrent = remember { date.get(Calendar.YEAR) - startDate.get(Calendar.YEAR) }
    val monthCurrent = remember { date.get(Calendar.MONTH) - startDate.get(Calendar.MONTH) + 1 }
    val dayCurrent = remember { date.get(Calendar.DAY_OF_MONTH) - startDate.get(Calendar.DAY_OF_MONTH) }

    val yearList by remember { mutableStateOf((startDate.get(Calendar.YEAR)..endDate.get(Calendar.YEAR)).map { it.toString() }) }
    var monthList by remember { mutableStateOf((1..12).map { if (it <= 9) "0$it" else it.toString() }) }
    var dayList by remember { mutableStateOf((1..date.getActualMaximum(Calendar.DAY_OF_MONTH)).map { if (it <= 9) "0$it" else it.toString() }) }

    val yearState = rememberFWheelPickerState(yearCurrent)
    val monthState = rememberFWheelPickerState(monthCurrent)
    val dayState = rememberFWheelPickerState(dayCurrent)

    LaunchedEffect(yearState) {
        snapshotFlow { yearState.currentIndex }
            .collect { index ->
                if (index >= 0) {
                    monthList = (1..if (yearList[index] == endDate.get(Calendar.YEAR).toString()) (endDate.get(Calendar.MONTH) + 1) else 12).map { if (it <= 9) "0$it" else it.toString() }
                    val select = Calendar.getInstance().apply { set(yearList[index].toInt(), monthList[if (monthState.currentIndex >= monthList.size) (monthList.size - 1) else monthState.currentIndex].toInt() - 1, 1) }
                    dayList = (1..select.getActualMaximum(Calendar.DAY_OF_MONTH)).map { if (it <= 9) "0$it" else it.toString() }
                }
            }
    }
    LaunchedEffect(monthState) {
        snapshotFlow { monthState.currentIndex }
            .collect { index ->
                if (index >= 0) {
                    val select = Calendar.getInstance().apply { set(yearList[yearState.currentIndex].toInt(), monthList[index].toInt() - 1, 1) }
                    dayList = (1..select.getActualMaximum(Calendar.DAY_OF_MONTH)).map { if (it <= 9) "0$it" else it.toString() }
                }
            }
    }
    Dialog(onDismissRequest = onDismiss, properties = DialogProperties(usePlatformDefaultWidth = false)) {
        Column(modifier = Modifier.fillMaxSize()) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .clickable { onDismiss() })
            Column(modifier = Modifier.background(color = Color.White, RoundedCornerShape(topEnd = 16.dp, topStart = 16.dp))) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 15.dp, vertical = 10.dp)
                ) {
                    Button(onClick = { onDismiss() }, colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent)) {
                        Text(text = "Cancel", color = Color(0xffb0b0b0), fontSize = 14.sp, lineHeight = 18.sp, fontWeight = FontWeight.Medium)
                    }
                    Text(
                        modifier = Modifier.align(Alignment.Center),
                        text = "Salary Receipt Date",
                        fontSize = 16.sp,
                        lineHeight = 20.sp,
                        color = Color(0xff2a292a),
                        fontWeight = FontWeight.Medium
                    )
                    Button(modifier = Modifier.align(Alignment.CenterEnd), colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent), onClick = {
                        onCall(yearList[yearState.currentIndex], monthList[monthState.currentIndex], dayList[dayState.currentIndex])
                    }) {
                        Text(text = "Done", color = Color(0xff2e9a6d), fontSize = 14.sp, lineHeight = 18.sp, fontWeight = FontWeight.Medium)
                    }
                }
                Row(modifier = Modifier.padding(vertical = 10.dp, horizontal = 20.dp)) {
                    FVerticalWheelPicker(
                        modifier = Modifier.weight(1f),
                        state = dayState,
                        count = dayList.size,
                        itemHeight = 45.dp,
                        focus = { FWheelPickerFocusVertical(dividerColor = Color(0xffe7e7e7)) },
                        unfocusedCount = 2
                    ) {
                        Text(text = dayList[it], fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a))
                    }
                    FVerticalWheelPicker(
                        modifier = Modifier.weight(1f),
                        state = monthState,
                        count = monthList.size,
                        itemHeight = 45.dp,
                        focus = { FWheelPickerFocusVertical(dividerColor = Color(0xffe7e7e7)) },
                        unfocusedCount = 2
                    ) {
                        Text(text = monthList[it], fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a))
                    }
                    FVerticalWheelPicker(
                        modifier = Modifier.weight(1f),
                        state = yearState,
                        count = yearList.size,
                        itemHeight = 45.dp,
                        focus = { FWheelPickerFocusVertical(dividerColor = Color(0xffe7e7e7)) },
                        unfocusedCount = 2
                    ) {
                        Text(text = yearList[it], fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a))
                    }
                }
            }
        }
    }
}