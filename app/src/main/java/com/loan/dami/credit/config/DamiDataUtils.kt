package com.loan.dami.credit.config

import android.content.Context
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.navigation.NavHostController
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.bean.DamiUserBean
import com.loan.dami.credit.config.DamiUtils.toDamiBean
import com.loan.dami.credit.config.DamiUtils.toDamiJson
import com.loan.dami.credit.ui.route.DamiLoginRoute
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking

object DamiDataUtils {
    private val Context.dataStore by preferencesDataStore("dami_credit_data_store")
    private val dataStore by lazy { DamiApplication.app.applicationContext.dataStore }
    const val USER_INFO = "user_info"
    const val MOBILE = "mobile"
    const val PERMISSION = "permission"
    const val FIRST_PERMISSION = "first_permission"
    const val LOGIN_TIME = "login_time"
    const val LOGIN_MARKET = "login_market"
    const val MARKET = "market"

    private var sessionId: String? = null

    fun isLogin(controller: NavHostController): Boolean {
        if (getSessionId().isEmpty()) {
            controller.navigate(DamiLoginRoute)
        }
        return isLogin()
    }

    fun isLogin(): Boolean = getSessionId().isNotEmpty()

    fun getMobile() = getUserInfo()?.username ?: ""

    fun getSessionId(): String {
        if (sessionId.isNullOrEmpty()) {
            sessionId = getUserInfo()?.sessionId
        }
        return sessionId ?: ""
    }

    fun addUser(bean: DamiUserBean?) {
        sessionId = bean?.sessionId
        putStrData(LOGIN_TIME, (System.currentTimeMillis() / 1000).toString())
        putStrData(MOBILE, bean?.username ?: "")
        putStrData(USER_INFO, bean.toDamiJson())
    }

    fun clear() {
        sessionId = null
        putStrData(USER_INFO, "")
    }

    private fun getUserInfo(): DamiUserBean? {
        val userStr = getStrData(USER_INFO, "")
        return if (userStr.isEmpty()) null else userStr.toDamiBean()
    }

    fun getStrData(key: String, default: String): String {
        var value = default
        runBlocking {
            value = dataStore.data.first()[stringPreferencesKey(key)] ?: default
        }
        return value
    }

    fun putStrData(key: String, value: String) {
        runBlocking {
            dataStore.edit {
                it[stringPreferencesKey(key)] = value
            }
        }
    }

    fun getBoolData(key: String, default: Boolean): Boolean {
        var value = default
        runBlocking {
            value = dataStore.data.first()[booleanPreferencesKey(key)] ?: default
        }
        return value
    }

    fun putBoolData(key: String, value: Boolean) {
        runBlocking {
            dataStore.edit {
                it[booleanPreferencesKey(key)] = value
            }
        }
    }
}