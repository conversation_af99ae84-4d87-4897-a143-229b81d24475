package com.loan.dami.credit.config


import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ConfigUpdate
import com.google.firebase.remoteconfig.ConfigUpdateListener
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.loan.dami.credit.R

object DamiConfigUtils {

    fun initConfig(){
        try {
            Firebase.remoteConfig.setDefaultsAsync(R.xml.dami_config)
            Firebase.remoteConfig.addOnConfigUpdateListener(object : ConfigUpdateListener{
                override fun onUpdate(configUpdate: ConfigUpdate) {
                }

                override fun onError(error: FirebaseRemoteConfigException) {
                }
            })
            Firebase.remoteConfig.fetchAndActivate()
        }catch (_: Exception){}
    }

    fun getApi() = try {
        Firebase.remoteConfig.getString("dami_api")
    }catch (_: Exception){
//        "https://api.pnfinancing.com/ph-dacr"
        "http://8.220.149.181/ph-dacr"
    }

    fun getWeb() = try {
        Firebase.remoteConfig.getString("dami_web_url")
    }catch (_: Exception){
        "https://www.pnfinancing.com/"
    }

    fun getPrivacy() = try {
        Firebase.remoteConfig.getString("dami_private_url")
    }catch (_: Exception){
        "https://api.pnfinancing.com/#/privacy"
    }

}