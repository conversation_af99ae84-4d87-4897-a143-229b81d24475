package com.loan.dami.credit.bean

import kotlinx.serialization.Serializable

@Serializable
data class DamiLoanConfirmBean(
    var productName: String = "",
    var productLogo: String = "",
    var amount: String = "",
    var displayAmount: String = "",
    var term: String = "",
    var displayTerm: String = "",
    var bankCard: String = "",
    var bankName: String = "",
    var cardType: String = "",
    var orderNo: String = "",
    var actual_period_type: String = "",
    var loan_agreement_url: String = "",
    var serviceFee: String = "",
    var displayServiceFee: String = "",
    var serviceFeeDetail: List<DamiLoanOtherBean>? = null,
    var countdownModule: DamiLoanOtherBean? = null,
    var loan_periods: List<DamiLoanOtherBean>? = null,
    var information: List<DamiLoanOtherBean>? = null,
    var repay_plans: List<DamiLoanOtherBean>? = null,
    var note: String = "",
    var available_amounts: List<String>? = null,
    var actual_amount: String = ""
)

@Serializable
data class DamiLoanOtherBean(
    val time: Long = 0,
    val content: String = "",
    val percent: String = "",
    val text: String = "",
    val selected: Int = 0,
    val period: Int = 0,
    val optional_period_type: Int = 0,
    var title: String = "",
    var value: String = "",
    var note: String = "",
    var period_text: String = "",
    var pay_time: String = "",
    var repay_money: String = "",
    var repay_principal: String = "",
    var lendDetailsUrl: String = "",
    var repay_interest: String = "",
    var dialog: DamiLoanOtherBean? = null
)