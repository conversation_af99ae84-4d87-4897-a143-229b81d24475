package com.loan.dami.credit.ui.view

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiRecreditRoute
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DamiRecreditView(route: DamiRecreditRoute) {

    val context = LocalContext.current
    val controller = LocalDamiController.current

    val model = viewModel { DamiModel<DamiState>(DamiState()) }

    DisposableEffect(model) {
        DamiApplication.topBar(Color(0xffecf5fa))
        DamiApplication.reCredit()
        DamiApplication.reCreditCall = { model.damiStart(context, controller, route.id) { controller.popBackStack() } }
        onDispose { DamiApplication.topBar(Color(0xff016b76)) }
    }

    var time by remember { mutableFloatStateOf(30000f) }
    var progress by remember { mutableFloatStateOf(0f) }

    LaunchedEffect(time) {
        if (time > 0) {
            delay(30)
            time -= 30
            progress = 1 - time / 30000
        }
    }

    DamiScrollView(model) {
        DamiFillImage(modifier = Modifier.padding(start = 70.dp, end = 70.dp, top = 70.dp), id = R.mipmap.img_recredit)
        Text(
            modifier = Modifier
                .padding(10.dp)
                .align(Alignment.CenterHorizontally),
            text = "Progress: ${(progress * 100).toInt()}%",
            fontSize = 12.sp,
            lineHeight = 14.sp,
            color = Color(0xffffb900),
            fontWeight = FontWeight.Medium
        )
        Box(modifier = Modifier.padding(horizontal = 20.dp), contentAlignment = Alignment.Center) {
            LinearProgressIndicator(
                modifier = Modifier
                    .padding(top = 9.dp, start = 10.dp, end = 10.dp)
                    .fillMaxWidth()
                    .border(6.dp, Color(0xff383332))
                    .height(22.dp),
                progress = { progress },
                gapSize = 0.dp,
                drawStopIndicator = {},
                color = Color(0xff82c3bd),
                trackColor = Color(0xff242120),
                strokeCap = StrokeCap.Butt
            )
            Slider(
                modifier = Modifier.fillMaxWidth(),
                value = progress,
                onValueChange = {},
                enabled = false,
                thumb = { DamiImage(modifier = Modifier.size(39.dp, 57.dp), id = R.mipmap.icon_recredit) },
                colors = SliderDefaults.colors(
                    disabledActiveTrackColor = Color.Transparent,
                    activeTrackColor = Color.Transparent,
                    inactiveTrackColor = Color.Transparent,
                    disabledInactiveTrackColor = Color.Transparent
                )
            )
        }
        Text(
            modifier = Modifier.padding(horizontal = 30.dp, vertical = 20.dp),
            text = buildAnnotatedString {
                append("Calculating your credit limit, just ")
                withStyle(style = SpanStyle(color = Color(0xff333333), fontWeight = FontWeight.Medium)) {
                    append("${(time / 1000).toInt()} seconds")
                }
                append(" Please wait patiently")
            },
            fontSize = 14.sp,
            lineHeight = 20.sp,
            color = Color(0xff6a6a6b),
            textAlign = TextAlign.Center
        )
        /*Text(
            modifier = Modifier
                .border(1.dp, Color(0xff82c3bd), CircleShape)
                .padding(horizontal = 29.dp, vertical = 12.dp)
                .align(Alignment.CenterHorizontally),
            text = "Try Again",
            fontSize = 18.sp,
            lineHeight = 25.sp,
            color = Color(0xff016b76),
            fontWeight = FontWeight.SemiBold
        )*/
    }
}