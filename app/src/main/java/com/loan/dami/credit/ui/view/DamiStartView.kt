package com.loan.dami.credit.ui.view

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiView
import com.loan.dami.credit.model.DamiStartModel

@Composable
fun DamiStartView() {
    val controller = LocalDamiController.current
    val model = viewModel { DamiStartModel() }
    LaunchedEffect(model) { model.start(controller) }

    DamiView(model = model) {
        Box(contentAlignment = Alignment.Center) {
            DamiImage(modifier = Modifier.fillMaxSize(), contentScale = ContentScale.FillBounds, id = R.mipmap.start_bg)
            DamiImage(modifier = Modifier.offset(y = (-80).dp).size(150.dp), id = R.mipmap.logo)
        }
    }

}