package com.loan.dami.credit.ui.view

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.bean.DamiAddressBean
import com.loan.dami.credit.bean.DamiPersonalBean
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiInput
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiPersonalModel
import com.loan.dami.credit.ui.dialog.DamiAddressDialog
import com.loan.dami.credit.ui.dialog.DamiPaydayDialog
import com.loan.dami.credit.ui.dialog.DamiSelectDialog
import com.loan.dami.credit.ui.dialog.DamiVerifyDialog
import com.loan.dami.credit.ui.route.DamiPersonalRoute

@Composable
fun DamiPersonalView(route: DamiPersonalRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current

    val model = viewModel { DamiPersonalModel(route) }
    val state by model.state.collectAsState()

    LaunchedEffect(model) { model.personalData(controller) }

    var url by remember { mutableStateOf<String?>(null) }

    url?.let {
        DamiVerifyDialog(it) {
            url = null
            if (it == 1) {
                controller.popBackStack()
            }
        }
    }

    val back = {
        model.dialogVerify(controller, 2, route.id) {
            if (it.dialog?.image_source?.isNotEmpty() == true) {
                url = it.dialog.image_source
            } else {
                controller.popBackStack(route, true)
            }
        }
    }

    BackHandler { back() }

    DamiScrollView(model, title = "Identity Information", onBack = { back() }, bottomBar = {
        Text(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
                .background(Color(0xff016b76), shape = CircleShape)
                .clickDelay { model.savePersonalData(context, controller) }
                .padding(14.dp),
            text = "Save",
            fontSize = 16.sp,
            lineHeight = 20.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }) {
        Box(
            modifier = Modifier
                .background(Color(0xff016b76))
                .padding(horizontal = 20.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            DamiFillImage(id = R.mipmap.top_bg_b)
            Text(
                modifier = Modifier.padding(horizontal = 20.dp),
                text = "Fill in personal information\ntruthfully and accurately, with a\n90% success rate",
                fontSize = 16.sp,
                lineHeight = 19.sp,
                color = Color(0xff2a292a),
                fontWeight = FontWeight.Medium
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xff016b76))
                .padding(top = 16.dp)
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(vertical = 12.dp)
        ) {
            state.bean?.items?.let { list ->
                val lastBean = list.lastOrNull { it.isInput() }
                list.forEach { DamiPersonalItem(controller, model, it, lastBean) }
            }
        }
    }
}

@Composable
fun <T : DamiState> DamiPersonalItem(controller: NavHostController, model: DamiModel<T>, bean: DamiPersonalBean, lastBean: DamiPersonalBean?) {
    val keyManager = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    bean.init()
    var valueState by remember { mutableStateOf(bean.value) }
    var dialogState by remember { mutableStateOf(false) }
    var paydayState by remember { mutableStateOf(false) }
    var addressState by remember { mutableStateOf<List<DamiAddressBean>?>(null) }

    if (dialogState) {
        bean.note?.let { data ->
            DamiSelectDialog(bean.title, data.map { it.name }) { i ->
                dialogState = false
                if (i != -1) {
                    valueState = data[i].name
                    bean.value = valueState
                    bean.type = data[i].type
                }
            }
        }
    }
    if (paydayState) {
        bean.note?.let { data ->
            DamiPaydayDialog(bean.title, data, onDismiss = { paydayState = false }) { one, two, type ->
                paydayState = false
                valueState = "$one - $two"
                bean.value = "$one - $two"
                bean.type = type
            }
        }
    }
    addressState?.let { list ->
        DamiAddressDialog(bean.title, list) { address ->
            addressState = null
            address?.let {
                valueState = it
                bean.value = it
            }
        }
    }

    Column(
        modifier = Modifier
            .padding(horizontal = 20.dp, vertical = 6.dp)
            .border(1.dp, Color(0xff82c3bd), RoundedCornerShape(4.dp))
            .background(Color.White, RoundedCornerShape(4.dp))
            .clickDelay(enable = !bean.isInput()) {
                keyManager?.hide()
                focusManager.clearFocus()
                if (bean.cate == "guerr_illa") {
                    if (bean.code == "werflo") {
                        paydayState = true
                    } else {
                        dialogState = true
                    }
                }
                if (bean.cate == "quali_fied") {
                    model.addressList(controller) {
                        addressState = it
                    }
                }
            }
            .padding(12.dp)
    ) {
        Text(modifier = Modifier.padding(vertical = 4.dp), text = bean.title, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
        HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp), color = Color(0xffd1d1d1))
        DamiInput(
            value = valueState,
            onValueChange = {
                bean.value = it
                valueState = it
            },
            placeholder = bean.subtitle,
            enabled = bean.isInput(),
            imeAction = if (bean.id == lastBean?.id) ImeAction.Done else ImeAction.Next,
            keyboardType = if (bean.isNumber()) KeyboardType.Number else if (bean.isEmail()) KeyboardType.Email else KeyboardType.Text,
            endView = { if (!bean.isInput()) DamiImage(modifier = Modifier.size(14.dp, 8.dp), id = R.mipmap.personal_arrow) }
        )
    }
}