package com.loan.dami.credit.api

import android.os.Build
import com.loan.dami.credit.BuildConfig
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.config.DamiConfigUtils
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiStrategy
import com.loan.dami.credit.config.DamiUtils
import com.loan.dami.credit.config.DamiUtils.toDamiEncode
import com.loan.dami.credit.config.FormDamiData
import io.github.aakira.napier.Napier
import io.ktor.client.HttpClient
import io.ktor.client.engine.okhttp.OkHttp
import io.ktor.client.plugins.HttpSend
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.compression.ContentEncoding
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.plugins.plugin
import io.ktor.client.request.forms.MultiPartFormDataContent
import io.ktor.client.request.forms.formData
import io.ktor.client.request.setBody
import io.ktor.content.TextContent
import io.ktor.http.Headers
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.parameters
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.put
import java.io.File
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

object DamiClient {

    fun getSignature(path: String, time: Long): String {
        val builder = StringBuilder("ailblackm${path}")
            .append("atediscrimin${DamiDataUtils.getSessionId()}")
            .append("bleincapa${DamiUtils.getAndroidId(DamiApplication.app)}")
            .append("calcriti${DamiApplication.googleId}")
            .append("egeprivil${Build.VERSION.RELEASE}")
            .append("gthlenandroid")
            .append("ickjoyst${Build.MODEL}")
            .append("ingsw${time}")
            .append("ntyuncertaigg-ph-mop")
            .append("sedclo${BuildConfig.VERSION_NAME}")
            .toString()
        val signature = StringBuilder()
        Mac.getInstance("HmacSHA256").apply {
            init(SecretKeySpec(DamiApplication.secretKey.toByteArray(), "HmacSHA256"))
            doFinal(builder.toByteArray()).forEach {
                signature.append(String.format("%02x", it))
            }
        }
        return signature.toString()
    }

    fun header(path: String, time: Long) = buildJsonObject {
        put("gthlen", "android")
        put("sedclo", BuildConfig.VERSION_NAME)
        put("ickjoyst", Build.MODEL)
        put("bleincapa", DamiUtils.getAndroidId(DamiApplication.app))
        put("egeprivil", Build.VERSION.RELEASE)
        put("ntyuncertai", "gg-ph-mop")
        put("atediscrimin", DamiDataUtils.getSessionId())
        put("calcriti", DamiApplication.googleId)
        put("aincompl", getSignature(path, time))
        put("ingsw", time.toString())
        put("erypott", (System.currentTimeMillis() / 1000).toString())
    }

    val httpClient by lazy {
        val client = HttpClient(OkHttp) {
            engine {
                config { followRedirects(true) }
            }
            defaultRequest {
                url("${DamiConfigUtils.getApi()}/dafreque/")
            }

            install(HttpTimeout) {
                requestTimeoutMillis = 40000
                connectTimeoutMillis = 40000
                socketTimeoutMillis = 40000
            }
            install(ContentNegotiation) {
                val json = Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                    namingStrategy = DamiStrategy()
                }
                json(json)
            }
            install(Logging) {
                level = LogLevel.ALL
                logger = object : Logger {
                    override fun log(message: String) {
                        Napier.e(message)
                    }
                }
            }
            install(ContentEncoding) {
                gzip()
            }
        }

        client.plugin(HttpSend).intercept {
            val time = System.currentTimeMillis()
            val header = header(path = it.url.toString().replace(DamiConfigUtils.getApi(), ""), time)
            execute(it.apply {
                if (method == HttpMethod.Patch) {
                    setBody(MultiPartFormDataContent(formData {
                        append("ageshort", header.toString().toDamiEncode())
                        (it.body as? TextContent)?.also { body ->
                            Json.parseToJsonElement(body.text).jsonObject.forEach { key, value ->
                                if (key == "attach") {
                                    val file = File(value.jsonPrimitive.content)
                                    val bytes = file.readBytes()
                                    append(key, bytes, Headers.build {
                                        append(HttpHeaders.ContentType, "image/jpg")
                                        append(HttpHeaders.ContentDisposition, "filename=\"image.jpg\"")
                                    })
                                } else if (key == "face"){
                                    val bytes = value.jsonPrimitive.content.toByteArray()
                                    append("attach", bytes, Headers.build {
                                        append(HttpHeaders.ContentType, "image/jpg")
                                        append(HttpHeaders.ContentDisposition, "filename=\"image.jpg\"")
                                    })
                                } else {
                                    append(key, value.jsonPrimitive.content)
                                }
                            }
                        }
                    }))
                } else {
                    setBody(FormDamiData(parameters {
                        try {
                            when (method) {
                                HttpMethod.Get -> append("ageshort", ((it.body as? TextContent)?.let { JsonObject(header.plus(Json.parseToJsonElement(it.text).jsonObject)) } ?: header).toString().toDamiEncode())
                                else -> {
                                    append("ageshort", header.toString().toDamiEncode())
                                    (it.body as? TextContent)?.also { body ->
                                        append(name = if (method == HttpMethod.Put) "ionisolat" else "ashcr", value = body.text.toDamiEncode())
                                    }
                                }
                            }
                        } catch (_: Exception) {
                            append("ageshort", header.toString().toDamiEncode())
                        }
                    }))
                }
                method = HttpMethod.Post
            })
        }
        client
    }
}