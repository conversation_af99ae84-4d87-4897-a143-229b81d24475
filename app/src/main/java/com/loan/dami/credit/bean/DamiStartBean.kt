package com.loan.dami.credit.bean

import kotlinx.serialization.Serializable

@Serializable
data class DamiStartBean(
    var result: Int = 0,
    var type: Int = 0,
    var msg: String = "",
    var url: String = "",
    var btn: List<DamiStartBean>? = null,
    var title: String = "",
    var message: String = "",
    var applyText: String = "",
    var cancelText: String = "",
    var text: String = "",
    var txt: String = "",
    var productId: String = "",
    var orderNo: String = "",
    var button: String = "")
