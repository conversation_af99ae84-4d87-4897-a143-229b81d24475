package com.loan.dami.credit.bean

import androidx.compose.ui.graphics.Color
import com.loan.dami.credit.R
import kotlinx.serialization.Serializable

@Serializable
data class DamiLoanListBean(
    var list: List<DamiLoanListBean>? = null,
    var detail: List<DamiTagBean>? = null,
    var buttonText: String = "",
    var buttonUrl: String = "",
    var date: String = "",
    var dateText: String = "",
    var dateValue: String = "",
    var inside: Int = 0,
    var loanDetailUrl: String = "",
    var loanTime: String = "",
    var moneyText: String = "",
    var noticeText: String = "",
    var orderAmount: String = "",
    var orderId: String = "",
    var orderStatus: String = "",
    var orderStatusDesc: String = "",
    var order_no: String = "",
    var productId: String = "",
    var productLogo: String = "",
    var productName: String = "",
    var repayTime: String = "",
    var term: String = ""
) {
    fun getItemBg() = when (orderStatus) {
        "180" -> R.mipmap.loan_list_bg_red
        "174" -> R.mipmap.loan_list_bg_orange
        else -> R.mipmap.loan_list_bg
    }

    fun getItemColor() = when (orderStatus) {
        "180" -> Color(0xffff4545)
        "174" -> Color(0xfff46e00)
        else -> Color(0xffb0b0b0)
    }

    fun showBtn() = orderStatus == "180" || orderStatus == "174"
}
