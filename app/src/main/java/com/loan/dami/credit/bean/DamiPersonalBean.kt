package com.loan.dami.credit.bean

import kotlinx.serialization.Serializable

@Serializable
data class DamiPersonalBean(
    var items: List<DamiPersonalBean>? = null,
    var note: List<DamiTagBean>? = null,
    var code: String = "",
    var cate: String = "",
    var id: String = "",
    var dateSelect: Int = 0,
    var status: Int = 0,
    var subtitle: String = "",
    var title: String = "",
    var statusName: String = "",
    var enable: Boolean = false,
    var value: String = "",
    var displayValue: String = "",
    var optional: Int = 0,
    var type: String = ""
) {

    fun init() {
        if (value.isNotEmpty() && !note.isNullOrEmpty()) {
            when (val item1 = note?.find { it.name == value }) {
                null -> {
                    when (val item2 = note?.find { it.type == value }) {
                        null -> value = ""
                        else -> {
                            value = item2.name
                            type = item2.type
                        }
                    }
                }

                else -> type = item1.type
            }
        }
    }

    fun isInput(): Boolean = cate == "selec_tion"

    fun isEmail(): Boolean = code == "eshfl"

    fun isNumber(): Boolean = code == "iveposit" || code == "pleap" || code == "acyliter" || code == "verobser"

    fun isBankTop(): Boolean = code == "utemin"

    fun isBank(): Boolean = code == "ranvete" || code == "bitrab" || code == "terpain"

    fun getText(): String = type.ifEmpty { value }
}

