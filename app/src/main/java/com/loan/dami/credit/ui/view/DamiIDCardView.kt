package com.loan.dami.credit.ui.view

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiIDCardModel
import com.loan.dami.credit.ui.dialog.DamiDialog
import com.loan.dami.credit.ui.dialog.DamiVerifyDialog
import com.loan.dami.credit.ui.route.DamiEKYCRoute
import com.loan.dami.credit.ui.route.DamiIDCardRoute

@Composable
fun DamiIDCardView(route: DamiIDCardRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current
    val model = viewModel { DamiIDCardModel(route) }
    val state by model.state.collectAsState()

    if (state.errorState) {
        DamiDialog(
            title = "Tips",
            content = "The photo of your ID is unclear and cannot be verified through live comparison. Please re-upload your ID photo.",
            left = "Cancel",
            right = "Go to change"
        ) {
            model.closeError()
            if (it == 2) {
                controller.navigate(DamiEKYCRoute(route.id, route.orderNo)) {
                    popUpTo(route) {
                        inclusive = true
                    }
                }
            }
        }
    }

    var url by remember { mutableStateOf<String?>(null) }

    url?.let {
        DamiVerifyDialog(it) {
            url = null
            if (it == 1) {
                controller.popBackStack(route, true)
            }
        }
    }

    val back = {
        model.dialogVerify(controller, 1, route.id) {
            if (it.dialog?.image_source?.isNotEmpty() == true) {
                url = it.dialog.image_source
            } else {
                controller.popBackStack()
            }
        }
    }

    BackHandler { back() }

    DamiScrollView(model, title = "Photo with ID Card", onBack = { back() }, bottomBar = {
        Text(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
                .background(Color(0xff016b76), shape = CircleShape)
                .clickDelay { model.idCardData(context, controller) }
                .padding(14.dp),
            text = "Next",
            fontSize = 16.sp,
            lineHeight = 20.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }) {
        Box(
            modifier = Modifier
                .background(Color(0xff016b76))
                .padding(horizontal = 20.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            DamiFillImage(id = R.mipmap.top_bg_b)
            Text(
                modifier = Modifier.padding(horizontal = 20.dp),
                text = "To ensure that the loan iso perated\nby you, the platform needs to\nverify your identity",
                fontSize = 16.sp,
                lineHeight = 19.sp,
                color = Color(0xff2a292a),
                fontWeight = FontWeight.Medium
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xff016b76))
                .padding(top = 16.dp)
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(vertical = 12.dp)
        ) {
            DamiFillImage(modifier = Modifier.padding(top = 8.dp, bottom = 8.dp, end = 20.dp), id = R.mipmap.img_id_card)
        }
    }
}