package com.loan.dami.credit.config

import android.annotation.SuppressLint
import android.content.Context
import android.database.Cursor
import android.icu.util.TimeZone
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.storage.StorageManager
import android.provider.Settings
import android.telephony.CellInfoCdma
import android.telephony.CellInfoGsm
import android.telephony.CellInfoLte
import android.telephony.CellInfoWcdma
import android.telephony.TelephonyManager
import android.util.Base64
import com.loan.dami.credit.DamiApplication
import id.zelory.compressor.Compressor
import id.zelory.compressor.constraint.default
import id.zelory.compressor.constraint.size
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.takeFrom
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonObjectBuilder
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.modules.EmptySerializersModule
import kotlinx.serialization.serializer
import java.io.File
import java.net.NetworkInterface
import java.util.Collections
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import kotlin.math.abs

object DamiUtils {

    inline fun buildDami(action: JsonObjectBuilder.() -> Unit) = buildJsonObject(action).toString()

    fun JsonObject.parameters(): String {
        val builder = StringBuilder()
        forEach {
            builder.append("&").append(it.key).append("=").append(it.value.jsonPrimitive.content)
        }
        return builder.toString()
    }

    suspend fun File.compress(context: Context) = Compressor.compress(context, this) {
        default(quality = 60)
        size(maxFileSize = 614_400, maxIteration = 100)
    }

    fun Cursor.getString(key: String) = try {
        val columnIndex = getColumnIndex(key)
        getString(columnIndex)
    } catch (_: Exception) {
        ""
    }

    fun String.parameters() = URLBuilder().takeFrom(Url(this)).build().parameters

    fun String.productId() = when {
        this.contains("lsefa") -> parameters()["lsefa"]
        this.contains("eamcr") -> parameters()["eamcr"]
        this.contains("productId") -> parameters()["productId"]
        else -> null
    } ?: ""

    fun String.orderNo() = when {
        this.contains("eadthr") -> parameters()["eadthr"]
        this.contains("undref") -> parameters()["undref"]
        this.contains("orderNo") -> parameters()["orderNo"]
        else -> null
    } ?: ""

    fun String.orderId() = when {
        this.contains("ionviolat") -> parameters()["ionviolat"]
        this.contains("orderId") -> parameters()["orderId"]
        else -> null
    } ?: ""

    fun String.scene() = when {
        this.contains("assgr") -> parameters()["assgr"]
        this.contains("scene") -> parameters()["scene"]
        else -> null
    } ?: ""

    fun String.money() = when {
        this.contains("₱") -> this
        this.contains(".") || this.contains(",") -> "₱ $this"
        else -> "₱ ${replace("(\\d)(?=(\\d{3})+\$)".toRegex(), "$1,")}"
    }

    fun String.nickName(): String {
        return this.replace("(\\w{3})\\w*(\\w{4})".toRegex(), "$1****$2")
    }

    fun Long.date(): String {
        val date = this
        return if (date > 0) {
            val h = "${date / 3600}"
            val m = "${(date % 3600) / 60}"
            val s = "${date % 60}"
            "${h.padStart(2, '0')}:${m.padStart(2, '0')}:${s.padStart(2, '0')}"
        } else {
            date.toString()
        }
    }

    fun Long.dates(): List<String> {
        val date = this
        return if (date > 0) {
            val m = "${(date % 3600) / 60}"
            val s = "${date % 60}"
            "${m.padStart(2, '0')}:${s.padStart(2, '0')}".map { it.toString() }
        } else {
            listOf("0", "0", ":", "0", "0")
        }
    }

    fun httpEnable() = try {
        (DamiApplication.app.applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager).let { manager ->
            manager.activeNetwork?.let { active ->
                manager.getNetworkCapabilities(active)?.let { capabilities ->
                    when {
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
                        else -> false
                    }
                } == true
            } == true
        } == true
    } catch (_: Exception) {
        false
    }

    inline fun <reified T> T.toDamiJson() = Json.Default.encodeToString(serializer = EmptySerializersModule().serializer(), value = this)

    inline fun <reified T> String.toDamiBean() = Json.Default.decodeFromString<T>(this)

    @OptIn(ExperimentalSerializationApi::class)
    inline fun <reified T> String.damiBean(): T {
        val json = Json {
            ignoreUnknownKeys = true
            isLenient = true
            namingStrategy = DamiStrategy()
        }
        return json.decodeFromString<T>(this.toDamiDecode())
    }

    fun String.toDamiAes() = try {
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        val keySpec = SecretKeySpec(DamiApplication.key.toByteArray(), "AES")
        val ivSpec = IvParameterSpec(DamiApplication.vi.toByteArray())
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec)
        val encrypt = cipher.doFinal(this.toByteArray())
        Base64.encodeToString(encrypt, Base64.NO_WRAP)
    } catch (_: Exception) {
        ""
    }

    fun String.toDamiEncode() = try {
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        val keySpec = SecretKeySpec(DamiApplication.key.toByteArray(), "AES")
        val ivSpec = IvParameterSpec(DamiApplication.vi.toByteArray())
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec)
        val encrypt = cipher.doFinal(this.toByteArray())
        val string = Base64.encodeToString(encrypt, Base64.NO_WRAP)
        Base64.encodeToString(string.toByteArray(), Base64.NO_WRAP)
    } catch (_: Exception) {
        ""
    }

    fun String.toDamiDecode() = try {
        val string = String(Base64.decode(this, Base64.NO_WRAP))
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        val keySpec = SecretKeySpec(DamiApplication.key.toByteArray(), "AES")
        val ivSpec = IvParameterSpec(DamiApplication.vi.toByteArray())
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec)
        val encrypt = cipher.doFinal(Base64.decode(string, Base64.NO_WRAP))
        String(encrypt)
    } catch (_: Exception) {
        ""
    }

    @SuppressLint("HardwareIds")
    fun getAndroidId(context: Context) = try {
        Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
    } catch (_: Exception) {
        ""
    }

    fun getIPAddress(): String {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val inetAddresses = interfaces.nextElement().inetAddresses
                while (inetAddresses.hasMoreElements()) {
                    val element = inetAddresses.nextElement()
                    val hostAddress = element.hostAddress ?: ""
                    if (!element.isLoopbackAddress && hostAddress.indexOf(":") < 0) {
                        return hostAddress
                    }
                }
            }
        } catch (_: Exception) {
        }
        return ""
    }

    fun getRoot() = try {
        var hasRootDir = false
        var rootDirs: Array<String>
        val dirCount = arrayOf(
            "/su",
            "/su/bin/su",
            "/sbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/data/local/su",
            "/system/xbin/su",
            "/system/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/system/bin/cufsdosck",
            "/system/xbin/cufsdosck",
            "/system/bin/cufsmgr",
            "/system/xbin/cufsmgr",
            "/system/bin/cufaevdd",
            "/system/xbin/cufaevdd",
            "/system/bin/conbb",
            "/system/xbin/conbb"
        ).also { rootDirs = it }.size
        for (i in 0 until dirCount) {
            val dir = rootDirs[i]
            if (File(dir).exists()) {
                hasRootDir = true
                break
            }
        }
        if (Build.TAGS != null && Build.TAGS.contains("test-keys") || hasRootDir) "1" else "0"
    } catch (_: Exception) {
        "0"
    }

    fun getMacAddress(): String {
        var mac = "02:00:00:00:00:00"
        try {
            val all: List<NetworkInterface> = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (nif in all) {
                if (!nif.name.equals("wlan0", true)) continue
                val macBytes: ByteArray = nif.getHardwareAddress() ?: return ""
                val res1 = StringBuilder()
                for (b in macBytes) {
                    res1.append(String.format("%02X:", b))
                }
                if (res1.isNotEmpty()) {
                    res1.deleteCharAt(res1.length - 1)
                }
                mac = res1.toString()
            }
        } catch (_: Exception) {
        }
        return if (mac.contains("02:00:00:00:00:00")) "" else mac
    }

    fun getHttpName(context: Context): String {
        if (!httpEnable()) {
            return "Bad Network"
        }
        try {
            (context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager)?.apply {
                activeNetworkInfo?.apply {
                    return if (isAvailable) {
                        if (type == ConnectivityManager.TYPE_WIFI) {
                            "WIFI"
                        } else if (type == ConnectivityManager.TYPE_MOBILE) {
                            when (subtype) {
                                TelephonyManager.NETWORK_TYPE_GSM, TelephonyManager.NETWORK_TYPE_GPRS, TelephonyManager.NETWORK_TYPE_CDMA, TelephonyManager.NETWORK_TYPE_EDGE, TelephonyManager.NETWORK_TYPE_1xRTT, TelephonyManager.NETWORK_TYPE_IDEN -> "2G"
                                TelephonyManager.NETWORK_TYPE_TD_SCDMA, TelephonyManager.NETWORK_TYPE_EVDO_A, TelephonyManager.NETWORK_TYPE_UMTS, TelephonyManager.NETWORK_TYPE_EVDO_0, TelephonyManager.NETWORK_TYPE_HSDPA, TelephonyManager.NETWORK_TYPE_HSUPA, TelephonyManager.NETWORK_TYPE_HSPA, TelephonyManager.NETWORK_TYPE_EVDO_B, TelephonyManager.NETWORK_TYPE_EHRPD, TelephonyManager.NETWORK_TYPE_HSPAP -> "3G"
                                TelephonyManager.NETWORK_TYPE_IWLAN, TelephonyManager.NETWORK_TYPE_LTE -> "4G"
                                TelephonyManager.NETWORK_TYPE_NR -> "5G"
                                else -> {
                                    if (subtypeName.equals("TD-SCDMA", ignoreCase = true)
                                        || subtypeName.equals("WCDMA", ignoreCase = true)
                                        || subtypeName.equals("CDMA2000", ignoreCase = true)
                                    ) {
                                        "3G"
                                    } else {
                                        "OTHER"
                                    }
                                }
                            }
                        } else {
                            "OTHER"
                        }
                    } else "OTHER"
                }
            }
        } catch (_: Exception) {
        }
        return "OTHER"
    }

    fun getGMT(): String {
        val offset = TimeZone.getDefault().rawOffset
        val hour = offset / (60000 * 60)
        val minutes = "${(offset / 60000) % 60}".padStart(2, '0')
        return "GMT${if (hour >= 0) "+" else "-"}${abs(hour).toString().padStart(2, '0')}:$minutes"
    }

    @SuppressLint("MissingPermission")
    fun getDBM(tm: TelephonyManager): String {
        var dbm = 0
        try {
            val cellInfoList = tm.allCellInfo
            if (null != cellInfoList) {
                for (cellInfo in cellInfoList) {
                    if (cellInfo is CellInfoGsm) {
                        val cellSignalStrengthGsm = cellInfo.cellSignalStrength
                        dbm = cellSignalStrengthGsm.dbm
                    } else if (cellInfo is CellInfoCdma) {
                        val cellSignalStrengthCdma = cellInfo.cellSignalStrength
                        dbm = cellSignalStrengthCdma.dbm
                    } else if (cellInfo is CellInfoWcdma) {
                        val cellSignalStrengthWcdma = cellInfo.cellSignalStrength
                        dbm = cellSignalStrengthWcdma.dbm
                    } else if (cellInfo is CellInfoLte) {
                        val cellSignalStrengthLte = cellInfo.cellSignalStrength
                        dbm = cellSignalStrengthLte.dbm
                    }
                }
            }
        } catch (_: Exception) {
        }
        return dbm.toString()
    }

    fun getSDType(context: Context): Int {
        try {
            (context.getSystemService(Context.STORAGE_SERVICE) as StorageManager).apply {
                val method = Class.forName("android.os.storage.StorageVolume").getMethod("isRemovable")
                (javaClass.getMethod("getVolumeList").invoke(this) as Array<*>).forEach {
                    val bool = method.invoke(it) as Boolean
                    if (bool) {
                        return 1
                    }
                }
            }
        } catch (_: Exception) {
        }
        return 0
    }

}