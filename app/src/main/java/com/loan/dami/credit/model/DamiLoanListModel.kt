package com.loan.dami.credit.model

import androidx.navigation.NavHostController
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiLoanListBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiLoanListRoute
import com.lt.compose_views.refresh_layout.RefreshContentStateEnum
import com.lt.compose_views.refresh_layout.RefreshLayoutState

data class DamiLoanListState(
    val type: Int,
    val page: Int = 1,
    val refresh: Boolean = false,
    val list: MutableList<DamiLoanListBean> = mutableListOf()
) : DamiState()

class DamiLoanListModel(route: DamiLoanListRoute) : DamiModel<DamiLoanListState>(DamiLoanListState(type = route.type)) {
    val types = arrayOf(4, 7, 6, 5)

    fun refreshList(controller: NavHostController, type: Int) {
        data.list.clear()
        setData(data.copy(type = type, refresh = true, page = 1))

        emitData<DamiLoanListBean>(controller, request = { DamiApi.loanList(type = types[type], page = data.page) }, complete = { setData(data.copy(refresh = false)) }) {
            it.list?.let { list -> data.list.addAll(list) }
            if (!it.list.isNullOrEmpty()) setData(data.copy(page = data.page + 1))
        }
    }

    fun loadList(controller: NavHostController, type: Int, state: RefreshLayoutState) {
        emitData<DamiLoanListBean>(controller, request = { DamiApi.loanList(type = types[type], page = data.page) }, complete = { state.setRefreshState(RefreshContentStateEnum.Stop) }) {
            it.list?.let { list -> data.list.addAll(list) }
            if (!it.list.isNullOrEmpty()) setData(data.copy(page = data.page + 1))
        }
    }
}