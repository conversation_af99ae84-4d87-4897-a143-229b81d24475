package com.loan.dami.credit.ui.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.ui.route.DamiQualityRoute

@Composable
fun DamiQualityView() {
    val controller = LocalDamiController.current

    DamiScrollView(title = "Quality Introduce", onBack = { controller.popBackStack<DamiQualityRoute>(true) }) {
        DamiFillImage(
            modifier = Modifier
                .background(Color(0xff016b76))
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(horizontal = 20.dp, vertical = 50.dp),
            id = R.mipmap.img_quality
        )
    }
}