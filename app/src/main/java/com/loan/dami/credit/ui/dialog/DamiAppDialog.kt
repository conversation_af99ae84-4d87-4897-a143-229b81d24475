package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.loan.dami.credit.R
import com.loan.dami.credit.bean.DamiAlertBean
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.clickDelay

@Composable
fun DamiAppDialog(bean: DamiAlertBean, onCall: (Int) -> Unit) {
    Dialog(onDismissRequest = { onCall(0) }) {
        Box(contentAlignment = Alignment.BottomCenter) {
            DamiFillImage(id = R.mipmap.img_upload)
            Column(
                modifier = Modifier
                    .aspectRatio(2 / 1.5f)
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(text = "V ${bean.app_version_max}", fontSize = 24.sp, lineHeight = 28.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Bold)
                Text(
                    modifier = Modifier
                        .weight(1f)
                        .padding(vertical = 10.dp),
                    text = bean.dialog_content,
                    fontSize = 18.sp,
                    lineHeight = 24.sp,
                    color = Color(0xff2a292a),
                    textAlign = TextAlign.Center
                )
                Text(
                    modifier = Modifier
                        .padding(4.dp)
                        .fillMaxWidth()
                        .background(Color(0xff016b76), shape = CircleShape)
                        .clickDelay { onCall(1) }
                        .padding(14.dp),
                    text = "Update Now",
                    fontSize = 16.sp,
                    lineHeight = 20.sp,
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}