package com.loan.dami.credit.ui.view

import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.bean.DamiAlertBean
import com.loan.dami.credit.config.DamiConfigUtils
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiRouteUtils
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.config.DamiUtils.nickName
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.ui.dialog.DamiAppDialog
import com.loan.dami.credit.ui.dialog.DamiImageDialog
import com.loan.dami.credit.ui.route.DamiLoanListRoute
import com.loan.dami.credit.ui.route.DamiMineRoute
import com.loan.dami.credit.ui.route.DamiSettingRoute

@Composable
fun DamiMineView(route: DamiMineRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current

    var alertBean by remember { mutableStateOf<DamiAlertBean?>(null) }

    val model = viewModel { DamiModel<DamiState>(DamiState()) }
    LaunchedEffect(model) { model.alert(2) { if (it.dialog != null) alertBean = it } }

    alertBean?.let { alert ->
        if (alert.type == "1") {
            alert.dialog?.let {bean->
                DamiAppDialog(bean) {
                    alertBean = null
                    if (it == 1) {
                        try {
                            context.startActivity(Intent(Intent.ACTION_VIEW, bean.url.toUri()))
                        } catch (_: Exception) {
                        }
                    }
                }
            }
        } else {
            alert.dialog?.let { bean ->
                DamiImageDialog(bean) {
                    alertBean = null
                    if (it == 1) {
                        model.damiNavigate(context, controller, bean.url)
                    }
                }
            }
        }
    }

    DamiScrollView(
        model = model,
        topBar = {
            Row(modifier = Modifier.padding(start = 20.dp, top = 10.dp, bottom = 10.dp), verticalAlignment = Alignment.CenterVertically) {
                DamiImage(modifier = Modifier.size(78.dp), id = R.mipmap.user_pic)
                Spacer(
                    modifier = Modifier
                        .padding(horizontal = 20.dp)
                        .size(1.dp, 51.dp)
                        .background(brush = Brush.verticalGradient(listOf(Color.Transparent, Color.White, Color.Transparent)))
                )
                Text(text = DamiDataUtils.getMobile().nickName(), fontSize = 20.sp, lineHeight = 24.sp, color = Color(0xffffffff), fontWeight = FontWeight.Medium)
            }
        },
        bottomBarColor = Color.White,
        bottomBar = {
            Row {
                Box(Modifier.weight(1f), contentAlignment = Alignment.Center) {
                    DamiImage(
                        modifier = Modifier
                            .padding(13.dp)
                            .clickDelay { controller.popBackStack(route, true) }
                            .size(60.dp, 23.dp), id = R.mipmap.icon_home
                    )
                }
                Box(Modifier.weight(1f), contentAlignment = Alignment.Center) {
                    DamiImage(
                        modifier = Modifier
                            .padding(13.dp)
                            .size(60.dp, 23.dp), id = R.mipmap.icon_mine_s
                    )
                }
            }
        }
    ) {
        Row(
            modifier = Modifier
                .background(Color(0xff016b76))
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(20.dp)
                .shadow(5.dp, shape = RoundedCornerShape(10.dp))
                .background(Color.White)
                .padding(vertical = 20.dp)
        ) {
            Column(
                Modifier
                    .weight(1f)
                    .clickDelay { controller.navigate(DamiLoanListRoute(0)) }, horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DamiImage(modifier = Modifier.size(60.dp), id = R.mipmap.icon_all)
                Text(modifier = Modifier.padding(top = 20.dp), text = "All", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
            }
            Spacer(
                modifier = Modifier
                    .padding(top = 5.dp)
                    .size(1.dp, 51.dp)
                    .background(brush = Brush.verticalGradient(listOf(Color.White, Color(0xffffa074), Color.White)))
            )
            Column(
                Modifier
                    .weight(1f)
                    .clickDelay { controller.navigate(DamiLoanListRoute(1)) }, horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DamiImage(modifier = Modifier.size(60.dp), id = R.mipmap.icon_out)
                Text(modifier = Modifier.padding(top = 20.dp), text = "Outstanding", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
            }
            Spacer(
                modifier = Modifier
                    .padding(top = 5.dp)
                    .size(1.dp, 51.dp)
                    .background(brush = Brush.verticalGradient(listOf(Color.White, Color(0xffffa074), Color.White)))
            )
            Column(
                Modifier
                    .weight(1f)
                    .clickDelay { controller.navigate(DamiLoanListRoute(3)) }, horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DamiImage(modifier = Modifier.size(60.dp), id = R.mipmap.icon_settled)
                Text(modifier = Modifier.padding(top = 20.dp), text = "Settled", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
            }
        }
        DamiImage(
            modifier = Modifier
                .padding(vertical = 6.dp)
                .size(190.dp, 40.dp)
                .align(Alignment.CenterHorizontally), id = R.mipmap.mine_more
        )
        Row(
            modifier = Modifier
                .padding(horizontal = 20.dp, vertical = 6.dp)
                .shadow(5.dp, RoundedCornerShape(10.dp))
                .background(Color.White)
                .clickDelay { DamiRouteUtils.navigateWeb(controller, route.url) }
                .padding(10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            DamiImage(modifier = Modifier.size(40.dp), id = R.mipmap.icon_msg)
            Text(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 8.dp), text = "Online Services", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a)
            )
            DamiImage(modifier = Modifier.size(20.dp), id = R.mipmap.icon_mine_arrow)
        }
        Row(
            modifier = Modifier
                .padding(horizontal = 20.dp, vertical = 6.dp)
                .shadow(5.dp, RoundedCornerShape(10.dp))
                .background(Color.White)
                .clickDelay { DamiRouteUtils.navigateWeb(controller, DamiConfigUtils.getPrivacy()) }
                .padding(10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            DamiImage(modifier = Modifier.size(40.dp), id = R.mipmap.icon_privacy)
            Text(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 8.dp), text = "Privacy Agreement", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a)
            )
            DamiImage(modifier = Modifier.size(20.dp), id = R.mipmap.icon_mine_arrow)
        }
        Row(
            modifier = Modifier
                .padding(horizontal = 20.dp, vertical = 6.dp)
                .shadow(5.dp, RoundedCornerShape(10.dp))
                .background(Color.White)
                .clickDelay { controller.navigate(DamiSettingRoute) }
                .padding(10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            DamiImage(modifier = Modifier.size(40.dp), id = R.mipmap.icon_set)
            Text(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 8.dp), text = "Setting", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a)
            )
            DamiImage(modifier = Modifier.size(20.dp), id = R.mipmap.icon_mine_arrow)
        }
    }
}