package com.loan.dami.credit.model

import android.content.Context
import androidx.navigation.NavHostController
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiPersonalBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiPersonalRoute
import kotlinx.serialization.json.put

data class DamiPersonalState(val bean: DamiPersonalBean? = null) : DamiState()

class DamiPersonalModel(private val route: DamiPersonalRoute) : DamiModel<DamiPersonalState>(DamiPersonalState()) {

    fun personalData(controller: NavHostController) {
        emitData<DamiPersonalBean>(controller, request = { DamiApi.personalData(route.id) }) {
            setData(data.copy(bean = it))
        }
        addressList(controller, false)
    }

    fun savePersonalData(context: Context, controller: NavHostController) {
        showLoading()
        flowEmit(controller, request = {
            DamiApi.savePersonalData {
                put("eamcr", route.id)
                put("umecost", "eamcr")
                put("derten", "umecost")
                data.bean?.items?.forEach { put(it.code, it.getText()) }
            }
        }, loading = false, complete = { if (!it) closeLoading() }) {
            damiData(5, route.id, route.orderNo) {
                damiVerify(context, controller, route.id) { controller.popBackStack() }
            }
        }
    }
}