package com.loan.dami.credit

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.view.WindowCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.appupdate.AppUpdateOptions
import com.google.android.play.core.install.InstallState
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import com.loan.dami.credit.DamiApplication.Companion.app
import com.loan.dami.credit.DamiApplication.Companion.googleId
import com.loan.dami.credit.config.DamiConfigUtils
import com.loan.dami.credit.model.DamiMainModel
import io.github.aakira.napier.DebugAntilog
import io.github.aakira.napier.Napier
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {

    val model by viewModels<DamiMainModel>()

    private val options by lazy { AppUpdateOptions.defaultOptions(AppUpdateType.FLEXIBLE) }
    private val manager by lazy { AppUpdateManagerFactory.create(this) }
    private val callback by lazy {
        object : InstallStateUpdatedListener {
            override fun onStateUpdate(state: InstallState) {
                if (state.installStatus() == InstallStatus.DOWNLOADED) {
                    manager.completeUpdate()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        manager.appUpdateInfo.addOnSuccessListener {
            if (it.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                manager.startUpdateFlowForResult(it, this, options, 1)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        manager.unregisterListener(callback)
    }

    @SuppressLint("SourceLockedOrientationActivity")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        enableEdgeToEdge()
        WindowCompat.setDecorFitsSystemWindows(window, true)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        window.statusBarColor = Color.Transparent.toArgb()
        window.navigationBarColor = Color.White.toArgb()
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        if (BuildConfig.DEBUG) {
            Napier.base(DebugAntilog())
        }
        DamiConfigUtils.initConfig()
        manager.registerListener(callback)
        manager.appUpdateInfo.addOnSuccessListener {
            if (it.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE && it.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE)) {
                manager.startUpdateFlowForResult(it, this, options, 1)
            }
        }
        DamiApplication.reCredit = { model.reCredit() }
        DamiApplication.damiData = { model.data(this) }
        DamiApplication.topBar = { window.statusBarColor = it.toArgb() }
        model.market(this)
        intent?.extras?.getString("url")?.let { url ->
            if (DamiApplication.pushCall == null) {
                DamiApplication.pushUrl = url
            } else {
                DamiApplication.pushCall?.invoke(url)
            }
        }
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                googleId = AdvertisingIdClient.getAdvertisingIdInfo(app).id ?: ""
            } catch (_: Exception) {
            }
        }

        setContent {
            DamiNavigation()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        intent.extras?.getString("url")?.let { url ->
            if (DamiApplication.pushCall == null) {
                DamiApplication.pushUrl = url
            } else {
                DamiApplication.pushCall?.invoke(url)
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    DamiNavigation()
}