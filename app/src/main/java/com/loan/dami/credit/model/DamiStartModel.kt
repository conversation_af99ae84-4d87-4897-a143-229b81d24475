package com.loan.dami.credit.model

import androidx.navigation.NavHostController
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiHomeRoute
import com.loan.dami.credit.ui.route.DamiPrivacyRoute
import com.loan.dami.credit.ui.route.DamiStartRoute
import kotlinx.coroutines.delay

class DamiStartModel : DamiModel<DamiState>(DamiState()) {

    suspend fun start(controller: NavHostController) {
        val isFirst = DamiDataUtils.getBoolData(DamiDataUtils.FIRST_PERMISSION, false)
        showLoading()
        delay(1000)
        closeLoading()
        controller.navigate(if (isFirst) DamiHomeRoute else DamiPrivacyRoute) {
            popUpTo<DamiStartRoute> { inclusive = true }
        }
    }

}