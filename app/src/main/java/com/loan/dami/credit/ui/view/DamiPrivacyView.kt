package com.loan.dami.credit.ui.view

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.config.DamiConfigUtils
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiRouteUtils
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.ui.route.DamiHomeRoute
import com.loan.dami.credit.ui.route.DamiPrivacyRoute

@Composable
fun DamiPrivacyView() {
    val context = LocalContext.current
    val controller = LocalDamiController.current
    val model = viewModel { DamiModel<DamiState>(DamiState()) }
    val scrollState = rememberScrollState()
    var isBottom by rememberSaveable { mutableStateOf(false) }

    DisposableEffect(scrollState.value) {
        DamiApplication.topBar(Color(0xfff5b36f))
        if (scrollState.value == scrollState.maxValue) {
            isBottom = true
        }
        onDispose { DamiApplication.topBar(Color(0xff016b76)) }
    }

    DamiScrollView(model, containerColor = Color(0xfff5b36f), topBarColor = Color(0xfff5b36f), scrollState = scrollState, topBar = {
        Text(modifier = Modifier.align(Alignment.Center), text = buildAnnotatedString {
            withStyle(style = SpanStyle(shadow = Shadow(blurRadius = 5f))) {
                append("Privacy Policy & Terms")
            }
        }, fontSize = 26.sp, lineHeight = 37.sp, color = Color.White, fontWeight = FontWeight.Black)
    }, bottomBarColor = Color.White, bottomBar = {
        Text(modifier = Modifier.padding(top = 12.dp, bottom = 6.dp), text = buildAnnotatedString {
            append("Please read the ")
            withLink(link = LinkAnnotation.Clickable(tag = "", styles = TextLinkStyles(style = SpanStyle(color = Color(0xffffa724), textDecoration = TextDecoration.Underline))) {
                DamiRouteUtils.navigateWeb(controller, DamiConfigUtils.getPrivacy())
            }) {
                append("Privacy Policy")
            }
        }, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff6a6a6b))
        Text(
            modifier = Modifier
                .padding(horizontal = 20.dp, vertical = 6.dp)
                .fillMaxWidth()
                .background(Color(if (isBottom) 0xff016b76 else 0xffb2c3bd), shape = CircleShape)
                .clickDelay {
                    if (isBottom) {
                        DamiDataUtils.putBoolData(DamiDataUtils.FIRST_PERMISSION, true)
                        controller.navigate(DamiHomeRoute) {
                            popUpTo<DamiPrivacyRoute> { inclusive = true }
                        }
                    } else {
                        model.toast("Please read the Privacy Policy")
                    }
                }
                .padding(13.dp),
            text = "I have read and agree",
            fontSize = 18.sp,
            lineHeight = 22.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier
                .clickDelay { (context as Activity).finish() }
                .padding(7.dp), text = "Reject", fontSize = 18.sp, lineHeight = 22.sp, color = Color(0xffb4b4b4), fontWeight = FontWeight.Medium)
    }) {
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp, vertical = 7.dp)
                .background(Color.White, shape = RoundedCornerShape(10.dp))
                .padding(horizontal = 16.dp, vertical = 7.dp)
        ) {
            Text(modifier = Modifier.padding(top = 7.dp), text = "1.Why This Policy Matters", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(modifier = Modifier.padding(top = 7.dp), text = "• About US", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Dami Credit is a mobile application developed and operated by Putu Novi Finance Corp.(SEC Registration Number: CS201913599 CA Number: 1230). We take user privacy seriously and consider data protection a core part of how we build and run our services.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Consent Declaration", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "This Privacy Policy explains how we collect, use, store, and share your information when you use our app.\n" +
                        "By using this app, you confirm that you have read, understood, and agreed to the terms of this Privacy Policy. If you do not agree with any part of the policy, please stop using the app.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Scope of Application", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "This policy applies to all users and covers all data collected through your interactions with our services. Our goal is to help you clearly understand what we collect, why we collect it, and how we handle your data responsibly.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "2.What Types of Information We Collect",
                fontSize = 16.sp,
                lineHeight = 18.sp,
                color = Color(0xff333333),
                fontWeight = FontWeight.Bold
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "To enable app functionality and improve your experience, we may collect the following categories of information:",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Account-Related Information", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Basic account information: registered mobile phone number, user name, registration date, login record (IP address, time, device information);\n" +
                        "Operation log: click records, browsing history, and usage of in-app functions (for service optimization and troubleshooting).",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Personal Information", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Basic information: name, gender, date of birth, contact information (mobile phone number, email address, contact address).\n" +
                        "Identity information: personal identification documents (such as ID card, passport, driver's license), facial information.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Household Information", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Marital status, Marital status and selected contact details.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "• Transaction Account & Financial Information",
                fontSize = 14.sp,
                lineHeight = 16.sp,
                color = Color(0xff666666),
                fontWeight = FontWeight.Medium
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Transaction Account: Bank account information, e-wallet accounts.\n" +
                        "Financial Information: Income, number of credit cards, salary schedule.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Work Information", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Professional details: Employer name, job title, company contact information, industry (for corporate services, business cooperation or career functions).",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "3.Information Collected with Your Consent",
                fontSize = 16.sp,
                lineHeight = 18.sp,
                color = Color(0xff333333),
                fontWeight = FontWeight.Bold
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Based on functional needs, we collect the following information after you grant permissions in your device settings:",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Device Information Permissions", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Includes access to device identifiers such as Android ID, device status, and system version for device environment profiling, risk control, and account protection.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Camera Permission", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Used to take and upload photos of your ID, selfies, or perform facial recognition for identity verification and related features.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Memory Permission", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Used to temporarily store operational data or images locally to enhance the user experience.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Location Information Permissions", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Location data is used to assist with identity verification, risk assessment, and fraud prevention.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Storage Permissions", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Used to locally store loan-related documents such as contracts and repayment records for your convenience",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "Note: You can withdraw permissions at any time via Settings > App Permissions, though this may affect certain features.",
                fontSize = 14.sp,
                lineHeight = 16.sp,
                color = Color(0xff666666),
                fontWeight = FontWeight.Medium
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "4.How We Collect This Information", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Information You Provide Directly", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Forms: Submitted when registering accounts, applying for loans, or contacting customer support (e.g., personal details, home address, employment information).\n" +
                        "File Uploads:Upload identity documents via camera or storage permissions, as well as other files uploaded when communicating with customer service.\n" +
                        "Facial recognition data captured via camera permissions for liveness detection (to verify user is physically present) during loan approval.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "• Information Collected with Your Permission",
                fontSize = 14.sp,
                lineHeight = 16.sp,
                color = Color(0xff666666),
                fontWeight = FontWeight.Medium
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "When you use the app, we may automatically collect certain data through system logs, device identifiers, cookies, and similar technologies for service optimization and risk control.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "• Information Collected Automatically",
                fontSize = 14.sp,
                lineHeight = 16.sp,
                color = Color(0xff666666),
                fontWeight = FontWeight.Medium
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Upon your explicit consent, we may access specific device permissions such as camera, location, or storage to enable certain features.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "5.How We Use Your Information", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Your data is strictly used for the purposes stated below. We never use personal information for unauthorized purposes without your explicit consent.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• To Provide Core Services", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Used to enable core functions such as account registration, identity verification, loan application assessment, fund disbursement, and repayment management.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• To Improve Product Experience", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "By understanding user behavior and preferences, we continuously enhance product features and interface to improve your experience.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• To Assess Risk and Prevent Fraud", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "We analyze device data, location information, and account activity to detect potential risks and suspicious actions, helping to prevent fraudulent behavior.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• To Comply with Legal Requirements", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "To process and report your information in accordance with applicable laws, regulations, or regulatory authority requests.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "• To Provide Customer Support and Communication",
                fontSize = 14.sp,
                lineHeight = 16.sp,
                color = Color(0xff666666),
                fontWeight = FontWeight.Medium
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Used to respond to your inquiries, offer customer service, and send important updates or reminder messages.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "• For Data Analysis and Service Improvement",
                fontSize = 14.sp,
                lineHeight = 16.sp,
                color = Color(0xff666666),
                fontWeight = FontWeight.Medium
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "We perform anonymized statistical analysis of the collected data to support business insights and service enhancements.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "6.Who Sees Your Info (And Why)", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "We take your privacy seriously. We will never share your information with unauthorized third parties unless it is legally or operationally necessary. Below are the parties that may access your information and why:",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Internal Teams", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Customer Support Team : May access your personal and account information to assist you with inquiries, resolve issues, and provide relevant support services.\n" +
                        "Product Development Team : May analyze aggregated and anonymized data to improve app features, functionality, and user experience.\n" +
                        "Risk Management Team : May review your financial and account information to assess risks, prevent fraud, and ensure secure transactions.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Third-Party Service Providers", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Payment Processors : When processing payment transactions, your bank information is accessed to process payments securely.\n" +
                        "Cloud Service Providers : Store and manage data on our behalf, ensuring the security and availability of your information.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Legal and Regulatory Bodies", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Government Agencies : May access your information in response to legal requests, court orders, or to comply with applicable laws and regulations.\n" +
                        "Regulatory Bodies : May require access to your information to ensure compliance with industry standards and regulations.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Other Situations", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Joint Venture Partners : In cases of business collaborations or partnerships, your information may be shared to provide joint services or offerings.\n" +
                        "Mergers and acquisitions: In transactions involving company mergers, acquisitions, asset transfers, etc., your information may be transferred to third parties as part of such transactions, but we will require the new entity to continue to honor this Privacy Policy. We will explain the situation to you and obtain your consent before sharing.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "7.How Long We Keep Your Info", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "We retain your data in accordance with the provisions of the Philippines Data Privacy Act (Republic Act No. 10173) and its Implementing Rules and Regulations (IRR).",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Our Services Information", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "We will continue to keep the information related to your account during your use of our services to ensure the proper functioning of our services.\n" +
                        "After the account is cancelled, the direct identifiers (phone number, email) will be deleted within 30 days, and the transaction records will be retained until the end of the statutory retention period.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Legal or Regulatory Requirements", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Certain data, such as identity documents and transaction records, may be retained for a period mandated by laws and regulations (e.g., AML laws, tax rules), even after you stop using the service.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(
                modifier = Modifier.padding(top = 7.dp),
                text = "• For Dispute, Investigation, or Complaint Resolution",
                fontSize = 14.sp,
                lineHeight = 16.sp,
                color = Color(0xff666666),
                fontWeight = FontWeight.Medium
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "We may retain data while any disputes, legal investigations, or complaints are pending, and until they are fully resolved.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Secure Deletion and Anonymization", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Once the data is no longer needed, we will delete or anonymize it using secure and reasonable technical methods to prevent unauthorized access, disclosure, or use.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "8.Your Choices and Rights", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Right to Be Informed", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "You have the right to know the types of personal information we collect, the purposes and retention periods.\n" +
                        "We notify users through the Privacy Policy or Terms of Service..",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Access & Correct Data", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Right: Request a copy of your data or correct errors.\n" +
                        "How:  <EMAIL> or use App > Customer Service Center.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Delete Data", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Right: Ask to delete non-essential data (e.g., marketing tags).\n" +
                        "Limitations: Legal records (e.g., loan contracts) may be retained.\n" +
                        "Withdraw Consent\n" +
                        "Right: Opt out of marketing or data sharing.\n" +
                        "How: <EMAIL> or use App > Customer Service Center.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Complaints", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Right: Lodge complaints with the Philippine National Privacy Commission (NPC) .\n" +
                        "Contact:  <EMAIL> or use App > Customer Service Center.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "9.How We Keep Things Safe", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "We take your information security seriously and use technical and administrative measures to protect data and reduce risks:",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Technical Protections", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Data in transit: Encrypted with TLS.\n" +
                        "Data at rest: AES-256 for sensitive fields (e.g., ID numbers).\n" +
                        "Server address: https://api.pnfinancing.com/ph-dacr\n" +
                        "24/7 monitoring of suspicious traffic (e.g., multiple login attempts).",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Administrative Controls", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Only authorized employees can access data relevant to their roles (principle of least privilege).\n" +
                        "Staff handling user information must comply with privacy policies and receive regular security training.\n" +
                        "Strict data handling procedures to ensure information is used only for authorized purposes.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
            Text(modifier = Modifier.padding(top = 7.dp), text = "• Data Backup", fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff666666), fontWeight = FontWeight.Medium)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Regular data backup, stored on servers within the Philippines.\n" +
                        "Quarterly data recovery drills to ensure quick restoration.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "10.What About Kids?", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "Our services are only for users aged 18 and above. We do not knowingly collect personal information from anyone under 18 years old. If you are not of legal age, please do not use the app or submit any personal data.\n" +
                        "If we discover that we have collected data from someone underage, we will delete it as soon as possible.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "11.If This Policy Changes", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = "We may update this Privacy Policy from time to time to reflect legal changes, business needs, or service updates. If there are major changes, we will notify you through in-app messages, email, or other reasonable means.\n" +
                        "The revised policy will take effect after it is published, and your continued use of the app will be deemed as your acceptance of the new policy.\n" +
                        "If you disagree with the changes, you can stop using the app and delete your account.",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )

            Text(modifier = Modifier.padding(top = 7.dp), text = "12.Talk to Us", fontSize = 16.sp, lineHeight = 18.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold)
            Text(
                modifier = Modifier.padding(top = 5.dp, bottom = 7.dp),
                text = "If you have any privacy-related questions, suggestions, or complaints, you can contact us in the following ways:\n" +
                        "Email: <EMAIL>\n" +
                        "Mailing address: 5th Floor Rockwell Business Center Tower 1 , Meralco Avenue , SECOND DISTRICT, NATIONAL CAPITAL REGION (NCR), 1604",
                fontSize = 12.sp,
                lineHeight = 14.sp,
                color = Color(0xff999999)
            )
        }
    }
}