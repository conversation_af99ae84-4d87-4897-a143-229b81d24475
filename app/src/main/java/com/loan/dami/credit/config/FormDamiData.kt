package com.loan.dami.credit.config

import io.ktor.http.ContentType
import io.ktor.http.content.OutgoingContent
import io.ktor.http.Parameters
import io.ktor.http.encodeURLParameter
import io.ktor.http.withCharset

class FormDamiData(data: Parameters) : OutgoingContent.ByteArrayContent() {
    private val byteArray by lazy {
        buildString {
            data.entries().flatMap { e -> e.value.map { e.key to it } }.joinTo(this, "&") {
                val key = it.first.encodeURLParameter(spaceToPlus = true)
                run {
                    val value = it.second
                    "$key=$value"
                }
            }
        }.toByteArray()
    }

    override fun bytes() = byteArray

    override val contentLength = bytes().size.toLong()

    override val contentType = ContentType.Application.FormUrlEncoded.withCharset(Charsets.UTF_8)

}