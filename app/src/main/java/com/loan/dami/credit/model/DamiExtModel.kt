package com.loan.dami.credit.model

import android.content.Context
import android.provider.ContactsContract
import androidx.activity.result.ActivityResult
import androidx.navigation.NavHostController
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiExtBean
import com.loan.dami.credit.bean.DamiPersonalBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.config.DamiUtils.getString
import com.loan.dami.credit.ui.route.DamiExtRoute
import kotlinx.serialization.json.addJsonObject
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.put

data class DamiExtState(val list: List<DamiExtBean>? = null, val enable: Boolean = false, var index: Int = 0): DamiState()

class DamiExtModel(private val route: DamiExtRoute) : DamiModel<DamiExtState>(DamiExtState()){


    fun extData(controller: NavHostController) {
        emitData<DamiExtBean>(controller, request = { DamiApi.extData(route.id) }) {
            setData(data.copy(list = it.emergent?.list))
        }
    }

    fun saveExtData(context: Context, controller: NavHostController) {
        showLoading()
        flowEmit(controller, request = {
            DamiApi.saveExtData {
                put("eamcr", route.id)
                put("icecrev", "eamcr")
                put("oryfact", buildJsonArray {
                    data.list?.forEach {
                        addJsonObject {
                            put("odyblo", it.mobile)
                            put("aicmos", it.name)
                            put("iseprom", it.relation)
                            put("herfeat", it.number1)
                        }
                    }
                }.toString())

            }
        }, loading = false, complete = { if (!it) closeLoading() }) {
            damiData(7, route.id, route.orderNo) {
                damiVerify(context, controller, route.id) { controller.popBackStack() }
            }
        }
    }

    fun setEnable(bool: Boolean) {
        setData(data.copy(enable = bool))
        toast("Error, Please enter contacts")
    }

    fun setExtData(context: Context, result: ActivityResult) {
        result.data?.data?.let { uri ->
            val name = ContactsContract.Contacts.DISPLAY_NAME
            val mobile = ContactsContract.CommonDataKinds.Phone.NUMBER
            try {
                context.contentResolver.query(uri, arrayOf(name, mobile), null, null, null)?.use {
                    if (it.moveToFirst()) {
                        val list = data.list?.mapIndexed { i, bean -> if (data.index == i) bean.copy(name = it.getString(name), mobile = it.getString(mobile)) else bean }
                        setData(data.copy(list = list, enable = false))
                    } else {
                        setEnable(true)
                    }
                } ?: setEnable(true)
            } catch (_: Exception) {
                setEnable(true)
            }
        }
    }
}