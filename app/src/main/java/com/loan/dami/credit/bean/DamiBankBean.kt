package com.loan.dami.credit.bean

import kotlinx.serialization.Serializable

@Serializable
data class DamiBankBean(
    var cardType: Int = 0,
    var cardTypeIcon: String = "",
    var cardTypeName: String = "",
    var list: List<DamiBankBean>? = null,
    var item: List<DamiBankBean>? = null,
    var account: String = "",
    var bankName: String = "",
    var bindId: String = "",
    var isMain: Int = 0,
    var logo: String? = null,
    var option: DamiBankBean? = null,
    var status: Int = 0,
    var type: String = "",
    var firstName: String = "",
    var lastName: String = "",
    var middleName: String = "")
