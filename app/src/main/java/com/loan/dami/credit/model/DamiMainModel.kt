package com.loan.dami.credit.model

import android.content.Context
import androidx.lifecycle.viewModelScope
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiUserBean
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiDeviceUtils
import com.loan.dami.credit.config.DamiLocationUtils
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch

class DamiMainModel : DamiModel<DamiState>(DamiState()) {

    private var reCredit = 1
    fun reCredit() {
        if (reCredit == 1) {
            reCredit = 2
            viewModelScope.launch {
                while (reCredit == 2) {
                    emitData<DamiUserBean>(request = { DamiApi.reCredit() }) {
                        if (it.result == 1) {
                            reCredit = 1
                            DamiApplication.reCreditCall()
                        }
                    }
                    delay(10000)
                }
            }
        }
    }

    fun market(context: Context) {
        uploadMarket(context, DamiDataUtils.MARKET)
    }

    fun data(context: Context) {
        viewModelScope.launch {
            flow { emit(DamiLocationUtils.getLocationData(context)) }.catch {}.collect { flowEmit(request = { DamiApi.locationData(it) }) }
            flow { emit(DamiDeviceUtils.loanRateDes(context)) }.catch {}.collect { flowEmit(request = { DamiApi.loanRateDes(it) }) }
            flow { emit(DamiDeviceUtils.termInfoDes(context)) }.catch {}.collect { flowEmit(request = { DamiApi.termInfoDes(it) }) }
        }
    }
}