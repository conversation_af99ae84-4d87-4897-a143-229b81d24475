package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog

@Composable
fun DamiDialog(title: String, content: String, left: String, right: String, call: (type: Int) -> Unit) {

    Dialog(onDismissRequest = { call(0) }) {
        Column(modifier = Modifier.background(Color.White, shape = RoundedCornerShape(12.dp)), horizontalAlignment = Alignment.CenterHorizontally) {
            Text(modifier = Modifier.padding(top = 31.dp, start = 20.dp, end = 20.dp), text = title, fontSize = 18.sp, lineHeight = 22.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
            Text(modifier = Modifier.padding(horizontal = 24.dp, vertical = 16.dp), text = content, fontSize = 18.sp, lineHeight = 20.sp, color = Color(0xff6a6a6b), textAlign = TextAlign.Center)
            HorizontalDivider(color = Color(0xffd1d1d1))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { call(1) }
                        .padding(vertical = 15.dp),
                    textAlign = TextAlign.Center,
                    text = left,
                    fontSize = 18.sp,
                    lineHeight = 22.sp,
                    color = Color(0xff000000)
                )
                VerticalDivider(modifier = Modifier.height(56.dp), thickness = .5.dp, color = Color(0xffd1d1d1))
                Text(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { call(2) }
                        .padding(vertical = 15.dp),
                    textAlign = TextAlign.Center,
                    text = right,
                    fontSize = 18.sp,
                    lineHeight = 22.sp,
                    color = Color(0xff016b76),
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}