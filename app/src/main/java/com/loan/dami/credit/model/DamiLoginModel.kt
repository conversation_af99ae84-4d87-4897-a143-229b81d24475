package com.loan.dami.credit.model

import android.content.Context
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.core.text.isDigitsOnly
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavHostController
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiUserBean
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiLoginRoute
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

data class DamiLoginState(
    val number: String = DamiDataUtils.getStrData(DamiDataUtils.MOBILE, ""),
    val code: String = "",
    val time: Int = 0,
    val agree: Boolean = true,
    val showCode: Boolean = false
) : DamiState()

class DamiLoginModel : DamiModel<DamiLoginState>(DamiLoginState()) {

    fun startLogin(controller: NavHostController, keyboard: SoftwareKeyboardController?) {
        keyboard?.hide()
        if (data.agree) {
            showLoading()
            emitData<DamiUserBean>(controller, request = { DamiApi.startLogin(data.number, data.code) }, complete = {
                if (!it) {
                    closeLoading()
                    setData(data.copy(code = ""))
                }
            }, showToast = true, loading = false) {
                DamiDataUtils.addUser(it)
                damiData(1) {
                    closeLoading()
                    controller.popBackStack()
                }
            }
        } else showMsg()
    }

    fun loginCode(context: Context, controller: NavHostController, focusRequester: FocusRequester) {
        if (data.agree) {
            setData(data.copy(showCode = true))
            flowEmit(controller, request = { DamiApi.loginCode(data.number) }, showToast = true, complete = {
                viewModelScope.launch {
                    try {
                        SmsRetriever.getClient(context).startSmsUserConsent(null)
                    } catch (_: Exception) {
                    }
                    delay(500)
                    focusRequester.requestFocus()
                }
            }) {
                setData(data.copy(time = 60))
                timeDown()
            }
        } else showMsg()
    }

    fun clickBack(controller: NavHostController) {
        if (data.showCode) {
            setData(data.copy(showCode = false))
        } else {
            controller.popBackStack<DamiLoginRoute>(true)
        }
    }

    fun clickBtn(context: Context, controller: NavHostController, keyboard: SoftwareKeyboardController?, focusRequester: FocusRequester) {
        if (data.showCode) {
            startLogin(controller, keyboard)
        } else {
            loginCode(context, controller, focusRequester)
        }
    }

    private fun showMsg() {
        toast("Please read and agree to the privacy policy")
    }

    private fun timeDown() {
        viewModelScope.launch {
            while (data.time > 0) {
                delay(1000)
                setData(data.copy(time = data.time - 1))
            }
        }
    }

    fun setCode(controller: NavHostController, keyboard: SoftwareKeyboardController?, code: String) {
        if (code.isDigitsOnly()) {
            setData(data.copy(code = code))
        }
        if (code.length == 6 && data.agree) {
            startLogin(controller, keyboard)
        }
    }

    fun setNumber(number: String) {
        if (number.length <= 16 && number.isDigitsOnly()) {
            setData(data.copy(number = number))
        }
    }

    fun agree() {
        setData(data.copy(agree = !data.agree))
    }
}