package com.loan.dami.credit.ui.view

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.bean.DamiPersonalBean
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiInput
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiSizeText
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiBankModel
import com.loan.dami.credit.ui.dialog.DamiBankDialog
import com.loan.dami.credit.ui.route.DamiBankRoute

@Composable
fun DamiBankView(route: DamiBankRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current

    val model = viewModel { DamiBankModel(route) }
    val state by model.state.collectAsState()
    var indexState by remember { mutableIntStateOf(0) }

    LaunchedEffect(model) { model.bankData(controller) }

    DamiScrollView(model, title = "Withdrawal Info", onBack = { controller.popBackStack(route, true) }, bottomBar = {
        Text(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 10.dp),
            text = "After your confirmation, this account will be used as a receipt account to receive the funds.",
            fontSize = 14.sp,
            lineHeight = 18.sp,
            color = Color(0xff6a6a6b),
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp, bottom = 12.dp)
                .fillMaxWidth()
                .background(Color(0xff016b76), shape = CircleShape)
                .clickDelay { model.saveBankData(context, controller, indexState) }
                .padding(14.dp),
            text = "Submit",
            fontSize = 16.sp,
            lineHeight = 20.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }) {

        Box(
            modifier = Modifier
                .background(Color(0xff016b76))
                .padding(horizontal = 20.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            DamiFillImage(id = R.mipmap.top_bg_b)
            Text(
                modifier = Modifier.padding(horizontal = 20.dp),
                text = "Please select the most convenient\nway for you to withdraw",
                fontSize = 16.sp,
                lineHeight = 19.sp,
                color = Color(0xff2a292a),
                fontWeight = FontWeight.Medium
            )
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xff016b76))
                .padding(top = 16.dp)
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(vertical = 12.dp)
        ) {
            Row(
                modifier = Modifier
                    .padding(vertical = 8.dp, horizontal = 20.dp)
                    .background(Color(0xffdcebf4), shape = RoundedCornerShape(10.dp))
                    .padding(5.dp)
            ) {
                state.list?.forEachIndexed { i, item ->
                    DamiSizeText(
                        modifier = Modifier
                            .weight(1f)
                            .padding(4.dp)
                            .shadow(5.dp, shape = RoundedCornerShape(10.dp))
                            .background(Color.White)
                            .clickDelay { indexState = i }
                            .padding(horizontal = 5.dp, vertical = 10.dp),
                        text = item.title,
                        fontSize = 16.sp,
                        lineHeight = 20.sp,
                        color = Color(if (i == indexState) 0xff016b76 else 0xffb0b0b0),
                        textAlign = TextAlign.Center
                    )
                }
            }
            state.list?.forEachIndexed { i, item ->
                AnimatedVisibility(i == indexState) {
                    Column {
                        item.items?.let { list ->
                            val lastBean = list.lastOrNull { it.isInput() }
                            val typeState = list.find { it.isBankTop() }?.value?.isNotEmpty() == true
                            list.forEachIndexed { index, bean ->
                                if ((!typeState && bean.isBank()).not()) {
                                    DamiBankItem(model, state.autoState, bean = bean, lastBean = lastBean)
                                }
                                if (state.autoState != 0 && list.size == index + 1) {
                                    model.setAutoState(0)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DamiBankItem(model: DamiBankModel, autoState: Int, bean: DamiPersonalBean, lastBean: DamiPersonalBean?) {
    val keyManager = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    bean.init()
    var valueState by remember { mutableStateOf(bean.value) }
    var dialogState by remember { mutableStateOf(false) }
    var popState by remember { mutableStateOf(false) }
    if (autoState == 1 && bean.isBank() && bean.value.isEmpty()) {
        bean.value = bean.displayValue
        valueState = bean.displayValue
    }

    if (dialogState) {
        bean.note?.let { data ->
            DamiBankDialog(bean.title, data) { i ->
                dialogState = false
                if (i != -1) {
                    valueState = data[i].name
                    bean.value = valueState
                    bean.type = data[i].type
                    model.setAutoState(2)
                }
            }
        }
    }

    Box(modifier = Modifier.padding(horizontal = 20.dp, vertical = 6.dp)) {
        Column(
            modifier = Modifier
                .border(1.dp, Color(0xff82c3bd), RoundedCornerShape(4.dp))
                .background(Color.White, RoundedCornerShape(4.dp))
                .clickDelay(enable = !bean.isInput()) {
                    keyManager?.hide()
                    focusManager.clearFocus()
                    if (bean.cate == "guerr_illa") {
                        dialogState = true
                    }
                }
                .padding(12.dp)
        ) {
            Text(modifier = Modifier.padding(vertical = 4.dp), text = bean.title, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
            HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp), color = Color(0xffd1d1d1))
            DamiInput(
                value = valueState,
                onValueChange = {
                    bean.value = it
                    valueState = it
                    popState = bean.isBank() && bean.value.isEmpty() && bean.displayValue.isNotEmpty()
                },
                placeholder = bean.subtitle,
                enabled = bean.isInput(),
                imeAction = if (bean.id == lastBean?.id) ImeAction.Done else ImeAction.Next,
                keyboardType = if (bean.isNumber()) KeyboardType.Number else if (bean.isEmail()) KeyboardType.Email else KeyboardType.Text,
                endView = { if (!bean.isInput()) DamiImage(modifier = Modifier.size(14.dp, 8.dp), id = R.mipmap.personal_arrow) },
                onFocus = { popState = it.isFocused && bean.isBank() && bean.value.isEmpty() && bean.displayValue.isNotEmpty() }
            )
        }
        AnimatedVisibility(
            modifier = Modifier
                .padding(20.dp)
                .align(Alignment.TopEnd), visible = popState
        ) {
            Box(modifier = Modifier.size(105.dp, 40.dp)) {
                DamiFillImage(id = R.mipmap.pop_bg)
                Row(verticalAlignment = Alignment.CenterVertically) {
                    DamiSizeText(
                        modifier = Modifier
                            .clickable {
                                keyManager?.hide()
                                focusManager.clearFocus()
                                popState = false
                                model.setAutoState(1)
                            }
                            .padding(horizontal = 10.dp, vertical = 7.dp)
                            .weight(1f),
                        text = bean.displayValue,
                        fontSize = 16.sp,
                        lineHeight = 16.sp,
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold
                    )
                    DamiImage(
                        modifier = Modifier
                            .clickable { popState = false }
                            .padding(7.dp)
                            .size(12.dp), id = R.mipmap.pop_close
                    )
                }
            }
        }
    }
    if (bean.isBankTop() && bean.value.isEmpty()) {
        Text(
            modifier = Modifier.padding(horizontal = 20.dp, vertical = 6.dp),
            text = "Ensure that the account informations are correct, otherwise the transfermay fail.",
            fontSize = 14.sp,
            lineHeight = 18.sp,
            color = Color(0xffe74949)
        )
    }
}