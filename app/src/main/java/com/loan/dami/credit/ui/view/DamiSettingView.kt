package com.loan.dami.credit.ui.view

import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.BuildConfig
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiConfigUtils
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiRouteUtils
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiSizeText
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiSettingModel
import com.loan.dami.credit.ui.dialog.DamiAlertDialog

@Composable
fun DamiSettingView() {
    val context = LocalContext.current
    val controller = LocalDamiController.current
    val model = viewModel { DamiSettingModel() }

    var logout by remember { mutableStateOf(false) }
    if (logout) {
        DamiAlertDialog(title = "Logout", content = "Are you sure you want to logout your account?", left = "Logout", right = "Cancel") {
            logout = false
            if (it == 1) {
                model.logout(controller)
            }
        }
    }
    var logoff by remember { mutableStateOf(false) }
    if (logoff) {
        DamiAlertDialog(title = "Deactivate Account", content = "Are you sure you want to delete your account?", left = "Sure", right = "Cancel") {
            logoff = false
            if (it == 1) {
                model.logoff(controller)
            }
        }
    }

    DamiScrollView(model, title = "Setting", bottomBar = {
        Text(modifier = Modifier.padding(bottom = 16.dp), text = "Version V${BuildConfig.VERSION_NAME}", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff6a6a6b))
        Text(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .border(1.dp, color = Color(0xffd1d1d1), shape = CircleShape)
                .background(Color.White, shape = CircleShape)
                .clickDelay { logoff = true }
                .padding(14.dp),
            text = "Deactivate Account",
            fontSize = 16.sp,
            lineHeight = 20.sp,
            color = Color(0xffb0b0b0),
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
                .background(Color(0xfff43939), shape = CircleShape)
                .clickDelay { logout = true }
                .padding(14.dp),
            text = "Logout",
            fontSize = 16.sp,
            lineHeight = 20.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }) {
        DamiImage(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xff016b76))
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(top = 20.dp)
                .size(100.dp),
            id = R.mipmap.logo
        )
        Row(modifier = Modifier.padding(horizontal = 15.dp, vertical = 30.dp)) {
            Column(
                modifier = Modifier
                    .padding(horizontal = 5.dp)
                    .weight(1f)
                    .shadow(5.dp, RoundedCornerShape(10.dp))
                    .background(Color.White)
                    .clickDelay { DamiRouteUtils.navigateWeb(controller, DamiConfigUtils.getWeb()) }
                    .padding(horizontal = 15.dp, vertical = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DamiImage(modifier = Modifier.size(38.dp), id = R.mipmap.icon_web)
                Text(text = "Website", fontSize = 14.sp, lineHeight = 20.sp, color = Color(0xff6a6a6b))
                DamiSizeText(modifier = Modifier.padding(top = 30.dp), text = "www.pnfinancing.com", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a))
            }
            Column(
                modifier = Modifier
                    .padding(horizontal = 5.dp)
                    .weight(1f)
                    .shadow(5.dp, RoundedCornerShape(10.dp))
                    .background(Color.White)
                    .clickDelay { context.startActivity(Intent.createChooser(Intent(Intent.ACTION_SENDTO, "<EMAIL>".toUri()), "Email")) }
                    .padding(horizontal = 15.dp, vertical = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DamiImage(modifier = Modifier.size(38.dp), id = R.mipmap.icon_email)
                Text(text = "E-mail", fontSize = 14.sp, lineHeight = 20.sp, color = Color(0xff6a6a6b))
                DamiSizeText(modifier = Modifier.padding(top = 30.dp), text = "<EMAIL>", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a))
            }
        }
    }
}