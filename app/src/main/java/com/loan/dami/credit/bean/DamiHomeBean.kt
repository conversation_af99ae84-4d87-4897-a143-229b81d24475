package com.loan.dami.credit.bean

import androidx.compose.ui.graphics.Color
import kotlinx.serialization.Serializable

@Serializable
data class DamiHomeBean(
    var icon: DamiLinkBean? = null,
    var list: List<DamiItemBean>? = null,
    var recommendReloan: DamiItemBean? = null
) {
    fun banner(): List<DamiProductBean>? {
        return list?.find { it.type == "culti_vate" }?.item
    }

    fun card(): DamiProductBean? {
        return list?.find { it.type == "suffe_ring" || it.type == "poten_tial" }?.item?.first()
    }

    fun productList(): List<DamiProductBean>? {
        return list?.find { it.type == "secre_tion" }?.item
    }

    fun progressList(): List<DamiProductBean>? {
        return list?.find { it.type == "invis_ible" }?.item
    }
}

@Serializable
data class DamiItemBean(
    var type: String = "",
    var item: List<DamiProductBean>? = null
)

@Serializable
data class DamiLinkBean(
    var iconUrl: String = "",
    var linkUrl: String = "",
    var url: String = "",
    var imgUrl: String = ""
)

@Serializable
data class DamiProductBean(
    var verify: List<DamiVerifyBean>? = null,
    var result: Int = 0,
    var msg: String = "",
    var imgUrl: String = "",
    var agreement: List<DamiAgreementBean>? = null,
    var productDetail: DamiProductBean? = null,
    var userInfo: DamiUserBean? = null,
    var amountRange: String = "",
    var loanRateDes: String = "",
    var loanRate: String = "",
    var amountRangeDes: String = "",
    var id: String = "",
    var productName: String = "",
    var productDesc: String = "",
    var loan_rate: String = "",
    var termInfo: String = "",
    var termInfoImg: String = "",
    var productLogo: String = "",
    var buttonText: String = "",
    var termInfoDes: String = "",
    var url: String = "",
    var orderId: String = "",
    var amountDesc: String = "",
    var columnText: DamiTagBean? = null,
    var amountArr: List<String>? = null,
    var amount: String = "",
    var term_type: Int = 0,
    var termArr: List<Int>? = null,
    var termDesc: String = "",
    var orderNo: String = "",
    var order_no: String = "",
    var buttonUrl: String = "",
    var term: String = "",
    var title: String = "",
    var message: String = "",
    var complaintUrl: String = "",
    var buttoncolor: String = "",
    var recommendTag: String = "",
    var settlementCount: String = "",
    var dueTime: String = "",
    var text1: String = "",
    var text2: String = "",
    var totalAmount: String = "",
    var repayTime: String = "",
    var repayDes: String = "",
    var loan_process_text: String = "",
    var certify_finished: Int = -1,
    var loanTermText: String = "",
    var order_status_text: String = "",
    var amount_text: String = "",
    var date_text: String = "",
    var date: String = "",
    var loan_process_list: List<DamiTagBean>? = null,

    /**
     * 1：审核中
     * 2：待还款
     * 3：已逾期
     * 4：放款中
     * 5：更换绑卡
     * 6：原卡重试
     */
    var status2: Int = 0,
    var buttons: List<DamiButtonBean>? = null,
    var account: String = "",
    var account_text: String = "",
    var cancel_end_at: Long = 0,
    var period_list: List<DamiPeriodBean>? = null,
) {
    fun processBg() = when (status2) {
        1, 4 -> listOf(Color(0xffc0e1de), Color(0xff80b5ba))
        2, 3 -> listOf(Color(0xffffdcdc), Color(0xffefa3a3))
        else -> listOf(Color(0xffffedd6), Color(0xfffeb695))
    }

    fun processTitleBg() = when (status2) {
        1, 4 -> Color(0xff016b76)
        2, 3 -> Color(0xfff43939)
        else -> Color(0xffec8944)
    }

    fun processTime() = cancel_end_at - System.currentTimeMillis()/1000
}

@Serializable
data class DamiAgreementBean(
    var scene: Int = 0,
    var orderId: Int = 0,
    var productId: String = "",
    var title: String = "",
    var position: String = ""
)

@Serializable
data class DamiButtonBean(
    var changeCard: DamiButtonBean? = null,
    var retryCard: DamiButtonBean? = null,
    var button_text: String = "",
    var ifShow: Int = 0,
    var loanStatus: Int = 0,
    var type: String = ""
)

@Serializable
data class DamiPeriodBean(
    var loanRate: String = "",
    var period: String = "0",
    var periodDesc: String = ""
)