package com.loan.dami.credit.model

import androidx.navigation.NavHostController
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiHomeRoute
import com.loan.dami.credit.ui.route.DamiLoginRoute

class DamiSettingModel: DamiModel<DamiState>(DamiState()) {

    fun logout(controller: NavHostController) {
        flowEmit(controller, request = { DamiApi.logout() }) {
            DamiDataUtils.clear()
            controller.navigate(DamiLoginRoute) {
                popUpTo<DamiHomeRoute> {
                    inclusive = false
                }
            }
        }
    }

    fun logoff(controller: NavHostController) {
        flowEmit(controller, request = { DamiApi.logoff() }) {
            DamiDataUtils.clear()
            controller.navigate(DamiLoginRoute) {
                popUpTo<DamiHomeRoute> {
                    inclusive = false
                }
            }
        }
    }
}