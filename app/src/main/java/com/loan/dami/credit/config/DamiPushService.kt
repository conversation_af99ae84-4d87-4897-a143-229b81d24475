package com.loan.dami.credit.config


import android.annotation.SuppressLint
import android.app.PendingIntent
import android.content.Intent
import android.os.Bundle
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.google.firebase.messaging.RemoteMessage
import com.google.firebase.messaging.FirebaseMessagingService
import com.loan.dami.credit.MainActivity
import com.loan.dami.credit.R

class DamiPushService  : FirebaseMessagingService(){

    override fun onNewToken(token: String) {
        super.onNewToken(token)
    }

    @SuppressLint("MissingPermission")
    override fun onMessageReceived(message: RemoteMessage) {
        super.onMessageReceived(message)
        try {
            val intent = Intent(this@DamiPushService, MainActivity::class.java)
            try {
                val bundle = Bundle()
                bundle.putString("url", message.data["url"])
                intent.putExtras(bundle)
            } catch (_: Exception) {
            }
            NotificationManagerCompat.from(this).notify(System.currentTimeMillis().toInt(), NotificationCompat.Builder(this, "dami_credit")
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle(message.notification?.title)
                .setContentText(message.notification?.body)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                .setAutoCancel(true)
                .setContentIntent(PendingIntent.getActivity(this@DamiPushService, System.currentTimeMillis().toInt(), intent, PendingIntent.FLAG_IMMUTABLE))
                .build())
        } catch (_: Exception) {
        }
    }

}