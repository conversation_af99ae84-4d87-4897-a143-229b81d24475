package com.loan.dami.credit.bean

import kotlinx.serialization.Serializable

@Serializable
data class DamiIDCardBean(
    var accessKey: String = "",
    var result_code: String = "",
    var biz_url: String = "",
    var biz_token: String = "",
    var error: String = "",
    var ifCanClick: Int = 0,
    var secretKey: String = "",
    var time: String = "",
    var url: String = "",
    var user_uuid: String = "")
