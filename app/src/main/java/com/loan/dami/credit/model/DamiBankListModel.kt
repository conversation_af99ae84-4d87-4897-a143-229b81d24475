package com.loan.dami.credit.model

import android.content.Context
import androidx.navigation.NavHostController
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiBankBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiBankListRoute
import com.loan.dami.credit.ui.route.DamiBankRoute

data class DamiBankListState(val list: List<DamiBankBean>? = null, val bean: DamiBankBean? = null) : DamiState()

class DamiBankListModel(private val route: DamiBankListRoute) : DamiModel<DamiBankListState>(DamiBankListState()) {

    fun bankListData(controller: NavHostController) {
        emitData<DamiBankBean>(controller, request = { DamiApi.bankListData(route.id) }) {
            var bean: DamiBankBean? = null
            var size = 0
            it.list?.forEach { item ->
                if (bean == null) {
                    bean = item.item?.find { it.isMain == 1 }
                }
                if (size == 0) {
                    size = item.item?.size ?: 0
                }
            }
            if (size == 0) {
                addBank(controller)
            } else {
                setData(data.copy(list = it.list, bean = bean))
            }
        }
    }

    fun addBank(controller: NavHostController){
        controller.navigate(DamiBankRoute(route.id, route.orderNo, route.type, true)){
            popUpTo(route){ inclusive = true }
        }
    }

    fun bankCheck(context: Context, controller: NavHostController) {
        if (data.bean == null) {
            toast("Bank card not added or selected.")
        } else {
            bankCheckData(context, controller, route.orderNo, data.bean?.bindId ?: "", route.type)
        }
    }

    fun setBankData(bean: DamiBankBean) {
        setData(data.copy(bean = bean))
    }
}