package com.loan.dami.credit.config


import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Resources
import android.graphics.Point
import android.hardware.Sensor
import android.hardware.SensorManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Proxy
import android.net.wifi.WifiManager
import android.os.BatteryManager
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.os.SystemClock
import android.provider.Settings
import android.telephony.TelephonyManager
import android.view.WindowManager
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.google.firebase.crashlytics.internal.common.CommonUtils
import com.loan.dami.credit.BuildConfig
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.config.DamiUtils.buildDami
import com.loan.dami.credit.config.DamiUtils.toDamiAes
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.serialization.json.addJsonObject
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import java.text.DecimalFormat
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.math.pow
import kotlin.math.sqrt


object DamiDeviceUtils {

    fun initAdjust(context: Context, key: String) {
        try {
            Adjust.initSdk(AdjustConfig(context, key, if (BuildConfig.DEBUG) AdjustConfig.ENVIRONMENT_SANDBOX else AdjustConfig.ENVIRONMENT_PRODUCTION))
        } catch (_: Exception) {
        }
    }

    suspend fun marketData(context: Context): String = suspendCancellableCoroutine { coroutine ->
        InstallReferrerClient.newBuilder(context).build().also {
            it.startConnection(object : InstallReferrerStateListener {
                override fun onInstallReferrerSetupFinished(code: Int) {
                    try {
                        if (code == InstallReferrerClient.InstallReferrerResponse.OK) {
                            it.installReferrer.apply {
                                if (coroutine.isActive) {
                                    val market =
                                        "${installReferrer}&referrerClickTimestampSeconds=${referrerClickTimestampSeconds}&installBeginTimestampSeconds=${installBeginTimestampSeconds}"
                                    coroutine.resume(market.market())
                                }
                            }
                        } else {
                            if (coroutine.isActive) {
                                coroutine.resume("".market())
                            }
                        }
                    } catch (_: Exception) {
                        if (coroutine.isActive) {
                            coroutine.resume("".market())
                        }
                    }
                }

                override fun onInstallReferrerServiceDisconnected() {
                    if (coroutine.isActive) {
                        coroutine.resume("".market())
                    }
                }
            })
        }
    }

    private fun String.market(): String {
        return buildDami {
            put("dixappen", this@market)
            put("icesacrif", "(${System.getProperty("http.agent")})")
            put("ectobj", "(Android ${Build.VERSION.RELEASE}; ${Locale.getDefault()}; ${Build.MODEL}; Build/${Build.ID}; Proxy)")
        }
    }

    fun loanRateDes(context: Context): String {
        return buildDami {
            val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            val bm = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val wm = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
            try {
                put("ionpermiss", bm.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY))
            } catch (_: Exception) {
            }
            put("mptexe", Build.BRAND)
            put("okerev", tm.simOperator)
            put("icesacrif", Build.MODEL)
            put("ageman", DamiUtils.getAndroidId(context))
            put("derconsi", DamiUtils.getIPAddress())
            put("agemiscarri", DamiUtils.getRoot())
            put("itycredibil", if (CommonUtils.isEmulator()) "1" else "0")
            put("ushfl", DamiUtils.getMacAddress())
            try {
                val mi = ActivityManager.MemoryInfo()
                am.getMemoryInfo(mi)
                put("eelst", "${(mi.totalMem / 1024.0 / 1024 / 1024).format()}GB")
            } catch (_: Exception) {
            }
            put("entag", "android")
            put("clerecy", Build.VERSION.RELEASE)
            put("emeextr", "0")
            context.resources.displayMetrics.apply {
                put("ageim", "${widthPixels}x${heightPixels}")
            }
            put("assgr", 0)
            try {
                if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
                    Environment.getExternalStorageDirectory().let {
                        val statFs = StatFs(it.path)
                        val blockSize = statFs.blockSizeLong
                        val blockCount = statFs.blockCountLong
                        put("agegarb", "${(blockCount * blockSize / 1024.0 / 1024 / 1024).format()}GB")
                        val blockAvailable = statFs.availableBlocksLong
                        put("eldfi", "${(blockSize * blockAvailable / 1024.0 / 1024 / 1024).format()}GB")
                    }
                }
            } catch (_: Exception) {
            }
            try {
                Environment.getDataDirectory().let {
                    val statFs = StatFs(it.path)
                    val blockSize = statFs.blockSizeLong
                    val blockCount = statFs.blockCountLong
                    put("entenvironm", "${(blockCount * blockSize / 1024.0 / 1024 / 1024).format()}GB")
                    val blockAvailable = statFs.availableBlocksLong
                    put("cedexperien", "${(blockSize * blockAvailable / 1024.0 / 1024 / 1024).format()}GB")
                }
            } catch (_: Exception) {
            }
            try {
                put(
                    "uceprod", if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        val networkCapabilities = cm.activeNetwork
                        val activeNetwork = cm.getNetworkCapabilities(networkCapabilities)
                        if (activeNetwork?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) "1" else "0"
                    } else {
                        if (cm.activeNetworkInfo?.type == ConnectivityManager.TYPE_WIFI) "1" else "0"
                    }
                )
            } catch (_: Exception) {
            }
            try {
                val ssid = wm.connectionInfo.ssid.replace("<", "").replace(">", "")
                put("arysupplement", if (ssid.contains("unknown")) "" else ssid)
            } catch (_: Exception) {
            }
        }.toDamiAes()
    }

    fun Double.format(oldValue: String = ",", newValue: String = "."): String {
        val string = if (this > 0) DecimalFormat("#.##").format(this).replace(oldValue, newValue) else this.toString()
        return string
    }

    @SuppressLint("MissingPermission")
    fun termInfoDes(context: Context): String {
        return buildDami {
            val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            put("entag", "android")
            put("clerecy", Build.VERSION.RELEASE)
            put("essweakn", DamiDataUtils.getStrData(DamiDataUtils.LOGIN_TIME, ""))
            put("derconsi", DamiUtils.getIPAddress())
            put("allfootb", "")
            put("rshma", "")
            put("ealrev", "")
            put("iveattract", context.packageName)
            put("pleprinci", buildJsonObject {
                context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))?.apply {
                    val bm = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
                    val plugged = getIntExtra(BatteryManager.EXTRA_PLUGGED, -1)
                    val status = getIntExtra(BatteryManager.EXTRA_STATUS, -1)
                    val pct = bm.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
                    val scale = try {
                        val clazz = Class.forName("com.android.internal.os.PowerProfile")
                        clazz.getMethod("getBatteryCapacity").invoke(clazz.getConstructor(Context::class.java).newInstance(context)) as Double
                    } catch (_: Exception) {
                        0.0
                    }
                    put("ecteff", (scale * pct / 100).toInt())
                    put("fulbeauti", scale.toInt())
                    put("arysumm", pct)
                    put("fulaw", if (plugged == BatteryManager.BATTERY_PLUGGED_AC) 1 else 0)
                    put("isesurpr", if (status == BatteryManager.BATTERY_STATUS_CHARGING) 1 else 0)
                    put("norho", if (plugged == BatteryManager.BATTERY_PLUGGED_USB) 1 else 0)
                }
            })
            put("ncecomplia", DamiLocationUtils.gpsInfo)
            put("ateevalu", buildJsonObject {
                put("onycol", DamiUtils.getAndroidId(context))
                put("nceda", DamiApplication.googleId)
                put("ushfl", DamiUtils.getMacAddress())
                put("ageman", DamiUtils.getAndroidId(context))
                put("ualrit", System.currentTimeMillis())
                put("ndsgrou", SystemClock.elapsedRealtime())
                try {
                    put("acepl", Settings.Global.getInt(context.contentResolver, Settings.Global.ADB_ENABLED, 0) != 0)
                    put("blefavora", Proxy.getDefaultPort() != -1)
                    put("verho", Proxy.getDefaultHost() != null)
                } catch (_: Exception) {
                }
                put("agemiscarri", DamiUtils.getRoot() == "1")
                try {
                    Resources.getSystem().configuration.getLocales().get(0).apply {
                        put("ionrecommendat", language)
                        put("ttecasse", displayLanguage)
                        put("rrywo", isO3Country)
                        put("rumfo", isO3Language)
                    }
                } catch (_: Exception) {
                }
                put("akesn", tm.simOperator)
                put("anteleph", DamiUtils.getHttpName(context))
                try {
                    val sm = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
                    put("tersis", buildJsonArray {
                        sm.getSensorList(Sensor.TYPE_ALL).forEach {
                            addJsonObject {
                                put("gbyru", it.maximumRange)
                                put("antarrog", it.minDelay)
                                put("aicmos", it.name)
                                put("atehesit", it.power)
                                put("ageim", it.resolution)
                                put("antten", it.type)
                                put("allbasketb", it.vendor)
                                put("tlebat", it.version)
                            }
                        }
                    })
                } catch (_: Exception) {
                }
                put("urefig", DamiUtils.getGMT())
                put("hesclot", SystemClock.uptimeMillis())
            })
            put("ellsp", buildJsonObject {
                put("iondefinit", Build.BOARD)
                put("mptexe", Build.BRAND)
                put("eerpion", Runtime.getRuntime().availableProcessors())
                context.resources.displayMetrics.apply {
                    put("terfil", heightPixels)
                    put("ioncommiss", widthPixels)
                }
                put("ioninhibit", "${Build.BRAND} ${Build.MODEL}")
                put("ionconsiderat", Build.MODEL)
                try {
                    val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                    val point = Point()
                    wm.defaultDisplay.getRealSize(point)
                    val dm = context.resources.displayMetrics
                    val x = (point.x / dm.xdpi).toDouble().pow(2.0)
                    val y = (point.y / dm.ydpi).toDouble().pow(2.0)
                    put("blesta", sqrt(x + y).toString())
                } catch (_: Exception) {
                }
                put("ainbarg", Build.TIME)
                put("sayes", Build.VERSION.RELEASE)
                put("percop", Build.VERSION.SDK_INT)
                put("ainsust", "")
            })
            put("eseche", buildJsonObject {
                try {
                    val wm = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
                    val ip = wm.connectionInfo.ipAddress
                    put("derconsi", "${ip and 0xff}.${ip shr 8 and 0xff}.${ip shr 16 and 0xff}.${ip shr 24 and 0xff}")
                    val array = buildJsonArray {
                        wm.scanResults?.forEach {
                            addJsonObject {
                                put("uremat", it.BSSID)
                                put("ushfl", it.BSSID)
                                put("aicmos", it.SSID)
                                put("manfisher", it.SSID)
                            }
                        }
                    }
                    put("vetvel", array)
                    put("vershi", array.size)
                    wm.connectionInfo?.apply {
                        put("terwa", buildJsonObject {
                            put("aicmos", if (ssid.contains("unknown")) "" else ssid)
                            put("manfisher", if (ssid.contains("unknown")) "" else ssid)
                            put("uremat", if (bssid.contains("02:00:00:00:00:00")) "" else bssid)
                            put("ushfl", if (bssid.contains("02:00:00:00:00:00")) "" else bssid)
                        })
                    }
                } catch (_: Exception) {
                }
            })


            put("entenvironm", buildJsonObject {
                try {
                    val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                    val mi = ActivityManager.MemoryInfo()
                    am.getMemoryInfo(mi)
                    put("atenomin", mi.totalMem.toString())
                    put("urelegislat", mi.availMem.toString())
                } catch (_: Exception) {
                }
                try {
                    if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
                        Environment.getExternalStorageDirectory().let {
                            val statFs = StatFs(it.path)
                            val blockSize = statFs.blockSizeLong
                            val blockCount = statFs.blockCountLong
                            val blockAvailable = statFs.availableBlocksLong
                            put("ormst", (blockSize * blockCount).toString())
                            put("ngearra", (blockSize * blockAvailable))
                            put("ionexempt", (blockSize * (blockCount - blockAvailable)))
                        }
                    }
                } catch (_: Exception) {
                }
                try {
                    Environment.getDataDirectory().let {
                        val statFs = StatFs(it.path)
                        val blockSize = statFs.blockSizeLong
                        val blockCount = statFs.blockCountLong
                        put("undwo", (blockSize * blockCount).toString())
                        val blockAvailable = statFs.availableBlocksLong
                        put("nchpu", (blockSize * blockAvailable).toString())
                    }
                } catch (_: Exception) {
                }
                try {
                    put("eepcr", if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) 1 else 0)
                } catch (_: Exception) {
                }
                put("eammainstr", DamiUtils.getSDType(context))
            })
        }.toDamiAes()
    }
}