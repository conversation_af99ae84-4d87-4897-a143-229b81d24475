package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiConfigUtils
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiRouteUtils

@Composable
fun DamiPrivateDialog(onCall: (Int) -> Unit) {
    val controller = LocalDamiController.current

    val title = remember { listOf("Camera Access","Memory Access", "Location Access","Device Information Permissions","Storage Permission") }
    val content = remember {
        listOf(
            "Purpose: Upload documents and perform face scanning for identity verification.\nProtection: Only activated when you use these features, with encrypted data transmission and strict privacy safeguards.",
            "Purpose: Optimize app performance and reduce loading times.\nProtection: Used only for temporary cache data, with minimal storage impact.",
            "Purpose: Assist in verifying your location, assess risks (e.g., suspicious login alerts).\nProtection: You can choose \"Only While Using\" the app or disable precise location sharing.",
            "Purpose: Collect basic device details (e.g., model, OS version) for security verification and service optimization.\nProtection: Only necessary non-sensitive data is collected.",
            "Purpose: Cache data for service stability and save files you upload or download.\nProtection: Accesses only service-related files, without reading private data."
        )
    }

    Dialog(onDismissRequest = { onCall(0) }) {
        Column {
            DamiFillImage(id = R.mipmap.img_private)
            Column(
                modifier = Modifier
                    .background(brush = Brush.verticalGradient(listOf(Color(0xffffeabb), Color.White)), shape = RoundedCornerShape(bottomStart = 20.dp, bottomEnd = 20.dp))
                    .padding(horizontal = 12.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f, false)
                        .verticalScroll(rememberScrollState())
                ) {
                    Text(
                        text = "To provide you with secure, smooth, and personalized services, we need to request certain permissions. We will strictly protect your information and only use it within the scope of your authorization.",
                        fontSize = 12.sp,
                        lineHeight = 15.sp,
                        color = Color(0xff6a6a6b)
                    )
                    title.forEachIndexed { i, s ->
                        Column(
                            modifier = Modifier
                                .padding(vertical = 5.dp)
                                .border(1.dp, Color(0xff016b76), RoundedCornerShape(20.dp))
                                .background(Color.White, RoundedCornerShape(20.dp)),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                modifier = Modifier
                                    .background(Color(0xff016b76), RoundedCornerShape(bottomStart = 10.dp, bottomEnd = 10.dp))
                                    .padding(horizontal = 15.dp, vertical = 2.dp),
                                text = s,
                                fontSize = 16.sp,
                                lineHeight = 22.sp,
                                color = Color.White,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                modifier = Modifier.padding(top = 5.dp, start = 10.dp, end = 10.dp, bottom = 10.dp),
                                text = content[i],
                                fontSize = 12.sp,
                                lineHeight = 15.sp,
                                color = Color(0xff2a292a),
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }

                var agree by remember { mutableStateOf(false) }
                Row(modifier = Modifier.padding(top = 10.dp)) {
                    DamiImage(
                        modifier = Modifier
                            .clip(CircleShape)
                            .clickable { agree = !agree }
                            .padding(6.dp)
                            .size(16.dp),
                        id = if (agree) R.mipmap.icon_login_select_s else R.mipmap.icon_login_select_n
                    )
                    Text(modifier = Modifier.padding(start = 2.dp, top = 6.dp), text = buildAnnotatedString {
                        append("Please read the ")
                        withLink(link = LinkAnnotation.Clickable(tag = "", styles = TextLinkStyles(style = SpanStyle(color = Color(0xffffa724), textDecoration = TextDecoration.Underline))) {
                            DamiRouteUtils.navigateWeb(controller, DamiConfigUtils.getPrivacy())
                        }) {
                            append("Privacy Policy")
                        }
                    }, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff6a6a6b))
                }

                Row(modifier = Modifier.padding(top = 10.dp, bottom = 20.dp)) {
                    Text(
                        modifier = Modifier
                            .padding(horizontal = 6.dp)
                            .weight(1f)
                            .clickable { onCall(0) }
                            .border(1.dp, Color(0xff016b76), CircleShape)
                            .padding(vertical = 14.dp),
                        textAlign = TextAlign.Center,
                        text = "Cancel",
                        fontSize = 18.sp,
                        lineHeight = 20.sp,
                        color = Color(0xff016b76),
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        modifier = Modifier
                            .padding(horizontal = 6.dp)
                            .weight(1f)
                            .clickable(agree) { onCall(1) }
                            .background(Color(if (agree) 0xff016b76 else 0xffb2c3bd), CircleShape)
                            .padding(vertical = 15.dp),
                        textAlign = TextAlign.Center,
                        text = "Accept",
                        fontSize = 18.sp,
                        lineHeight = 22.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}