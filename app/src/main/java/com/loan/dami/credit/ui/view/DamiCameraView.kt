package com.loan.dami.credit.ui.view

import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.camera.core.AspectRatio
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiCoil
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.ui.route.DamiCameraRoute
import java.io.File
import java.util.concurrent.Executors

@Composable
fun DamiCameraView() {

    val context = LocalContext.current
    val lifecycle = LocalLifecycleOwner.current
    val controller = LocalDamiController.current

    var uri by rememberSaveable { mutableStateOf<Uri?>(null) }
    val file by remember { derivedStateOf { File("${context.filesDir.path}/camera.jpg") } }
    val preview = remember { Preview.Builder().build() }
    val outFile = remember { ImageCapture.OutputFileOptions.Builder(file).build() }

    val imageCapture = remember {
        ImageCapture.Builder()
            .setFlashMode(ImageCapture.FLASH_MODE_AUTO)
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
            .setTargetAspectRatio(AspectRatio.RATIO_16_9)
            .setJpegQuality(50)
            .build()
    }
    val savedCallback = remember {
        object : ImageCapture.OnImageSavedCallback {
            override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                uri = output.savedUri
            }

            override fun onError(exception: ImageCaptureException) {
                uri = null
            }
        }
    }
    val cameraView = remember { PreviewView(context) }

    DisposableEffect(Unit) {
        val provider = ProcessCameraProvider.getInstance(context)
        val providerGet = provider.get()
        val executor = ContextCompat.getMainExecutor(context)
        val listener = Runnable {
            try {
                providerGet.unbindAll()
                providerGet.bindToLifecycle(lifecycle, CameraSelector.DEFAULT_BACK_CAMERA, preview, imageCapture)
                preview.surfaceProvider = cameraView.surfaceProvider
            } catch (_: Exception) {
                controller.popBackStack()
            }
        }
        provider.addListener(listener, executor)
        onDispose {
            providerGet.unbindAll()
        }
    }
    BackHandler {
        uri?.also { uri = null } ?: controller.popBackStack<DamiCameraRoute>(true)
    }

    Column(modifier = Modifier.background(Color.Black)) {
        Box(modifier = Modifier.weight(1f)) {
            if (uri == null) {
                AndroidView(modifier = Modifier.fillMaxSize(), factory = { cameraView })
            } else {
                DamiCoil(modifier = Modifier.fillMaxSize(), contentScale = ContentScale.Crop, model = uri)
            }
            DamiImage(modifier = Modifier.fillMaxSize(), contentScale = ContentScale.Crop, id = R.mipmap.img_camera)
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .navigationBarsPadding()
                .height(146.dp)
                .padding(horizontal = 38.dp)
        ) {
            DamiImage(
                modifier = Modifier
                    .size(40.dp)
                    .align(Alignment.CenterStart)
                    .clickDelay {
                        if (uri == null) {
                            controller.popBackStack<DamiCameraRoute>(true)
                        } else {
                            uri = null
                        }
                    },
                id = R.mipmap.img_carmra_cancel
            )
            if (uri == null) {
                DamiImage(
                    modifier = Modifier
                        .size(72.dp)
                        .align(Alignment.Center)
                        .clickDelay {
                            imageCapture.takePicture(outFile, Executors.newSingleThreadExecutor(), savedCallback)
                        },
                    id = R.mipmap.img_carmra_click
                )
            } else {
                DamiImage(
                    modifier = Modifier
                        .size(40.dp)
                        .align(Alignment.CenterEnd)
                        .clickDelay {
                            controller.popBackStack()
                            DamiApplication.camearCall.invoke(file.path)
                        },
                    id = R.mipmap.img_carmra_sure
                )
            }
        }
    }


}