package com.loan.dami.credit.ui.view

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiCoil
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiTitle
import com.loan.dami.credit.config.DamiView
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiBankListModel
import com.loan.dami.credit.ui.route.DamiBankListRoute

@Composable
fun DamiBankListView(route: DamiBankListRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current
    val model = viewModel { DamiBankListModel(route) }
    val state by model.state.collectAsState()

    LaunchedEffect(model) { model.bankListData(controller) }

    DamiView(model, title = "Loan Confirmation", onBack = { controller.popBackStack(route, true) }, bottomBar = {
        Text(
            modifier = Modifier
                .padding(horizontal = 20.dp, vertical = 16.dp)
                .fillMaxWidth()
                .background(Color(0xff016b76), shape = CircleShape)
                .clickDelay { model.bankCheck(context, controller) }
                .padding(14.dp),
            text = "Confirm",
            fontSize = 16.sp,
            lineHeight = 20.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxWidth()
                .background(Color(0xff016b76))
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
        ) {
            LazyColumn(modifier = Modifier.weight(1f, false)) {
                items(items = state.list ?: listOf()) { bean ->
                    DamiTitle(modifier = Modifier.padding(top = 20.dp, bottom = 6.dp), title = bean.cardTypeName)
                    bean.item?.forEach { item ->
                        Column(
                            Modifier
                                .padding(horizontal = 20.dp, vertical = 6.dp)
                                .clip(RoundedCornerShape(16.dp))
                                .background(brush = Brush.verticalGradient(listOf(Color(0xffe8a78e), Color(0xffe09578))))
                                .clickable { model.setBankData(item) }
                                .padding(20.dp)
                        ) {
                            Box(modifier = Modifier.fillMaxWidth()) {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    item.logo?.let { if (it.isNotEmpty()) DamiCoil(modifier = Modifier.padding(end = 10.dp).size(36.dp), model = it) }
                                    Column {
                                        Text(text = item.bankName, fontSize = 16.sp, lineHeight = 18.sp, color = Color.White, fontWeight = FontWeight.Medium)
                                        if (item.status == 0) {
                                            Text(
                                                text = "The bank is under maintenance. Loans may be delayed. Please wait or choose another option",
                                                fontSize = 10.sp,
                                                lineHeight = 12.sp,
                                                color = Color(0xfffffda0)
                                            )
                                        }
                                    }
                                }
                                DamiImage(
                                    modifier = Modifier
                                        .size(20.dp)
                                        .align(Alignment.TopEnd), id = if (item.bindId == state.bean?.bindId) R.mipmap.icon_list_select_s else R.mipmap.icon_list_select_n
                                )
                            }
                            Text(
                                modifier = Modifier
                                    .padding(top = 8.dp)
                                    .fillMaxWidth()
                                    .background(brush = Brush.horizontalGradient(listOf(Color.Transparent, Color(0x30ffffff), Color.Transparent)))
                                    .padding(top = 8.dp),
                                text = "Receipt Account",
                                fontSize = 12.sp,
                                lineHeight = 18.sp,
                                color = Color.White
                            )
                            Row(
                                modifier = Modifier
                                    .background(brush = Brush.horizontalGradient(listOf(Color.Transparent, Color(0x30ffffff), Color.Transparent)))
                                    .padding(bottom = 8.dp)
                            ) {
                                Text(modifier = Modifier.weight(1f), text = item.account, fontSize = 16.sp, lineHeight = 20.sp, color = Color.White, fontWeight = FontWeight.Medium)
                                DamiImage(
                                    modifier = Modifier.size(24.dp, 22.dp),
                                    id = if (bean.cardType == 2) R.mipmap.icon_bank else if (bean.cardType == 1) R.mipmap.icon_e_wallet else R.mipmap.icon_cash_pickup
                                )
                            }
                        }
                    }
                }
            }
            Row(
                modifier = Modifier
                    .clickDelay { model.addBank(controller) }
                    .padding(10.dp)
                    .align(Alignment.CenterHorizontally), verticalAlignment = Alignment.CenterVertically
            ) {
                DamiImage(modifier = Modifier.size(20.dp), id = R.mipmap.icon_list_add)
                Text(modifier = Modifier.padding(start = 10.dp), text = "Add other payment methods", fontSize = 16.sp, lineHeight = 16.sp, color = Color(0xffffa724), fontWeight = FontWeight.Medium)
            }
        }
    }
}