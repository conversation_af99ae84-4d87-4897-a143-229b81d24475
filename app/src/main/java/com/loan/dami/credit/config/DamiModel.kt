package com.loan.dami.credit.config

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavHostController
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiAddressBean
import com.loan.dami.credit.bean.DamiAlertBean
import com.loan.dami.credit.bean.DamiBean
import com.loan.dami.credit.bean.DamiProductBean
import com.loan.dami.credit.bean.DamiStartBean
import com.loan.dami.credit.bean.DamiTagBean
import com.loan.dami.credit.bean.DamiUserBean
import com.loan.dami.credit.config.DamiUtils.damiBean
import com.loan.dami.credit.config.DamiUtils.orderNo
import com.loan.dami.credit.config.DamiUtils.productId
import com.loan.dami.credit.ui.route.DamiBankRoute
import com.loan.dami.credit.ui.route.DamiEKYCRoute
import com.loan.dami.credit.ui.route.DamiExtRoute
import com.loan.dami.credit.ui.route.DamiHomeRoute
import com.loan.dami.credit.ui.route.DamiIDCardRoute
import com.loan.dami.credit.ui.route.DamiJobRoute
import com.loan.dami.credit.ui.route.DamiLoanConfirmRoute
import com.loan.dami.credit.ui.route.DamiLoginRoute
import com.loan.dami.credit.ui.route.DamiPersonalRoute
import io.ktor.client.call.NoTransformationFoundException
import io.ktor.client.plugins.HttpRequestTimeoutException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch

open class DamiState()

open class DamiModel<T : DamiState>(t: T) : ViewModel() {
    var startTime = System.currentTimeMillis()
    private val dataState = MutableStateFlow(t)
    val state: StateFlow<T> = dataState.asStateFlow()
    val data: T
        get() = state.value

    fun setData(t: T) {
        dataState.value = t
    }

    private val loadState = MutableStateFlow(false)
    val loading = loadState.asStateFlow()

    fun showLoading() {
        loadState.value = true
    }

    fun closeLoading() {
        loadState.value = false
    }

    private val toastState = MutableStateFlow("")
    val toast = toastState.asStateFlow()

    open fun toast(str: String) {
        toastState.value = str
    }

    fun retryData(controller: NavHostController, orderNo: String, onResult: () -> Unit) {
        flowEmit(controller, request = { DamiApi.bankRetryData(orderNo) }) {
            onResult()
        }
    }

    fun dialogVerify(controller: NavHostController, type: Int, id: String, complete: (DamiAlertBean) -> Unit) {
        emitData<DamiAlertBean>(controller, request = { DamiApi.dialog(type, id) }) { complete(it) }
    }

    fun alert(type: Int, complete: (DamiAlertBean) -> Unit) {
        emitData<DamiAlertBean>(request = { DamiApi.alert(type) }) { complete(it) }
    }

    fun bankCheckData(context: Context, controller: NavHostController, orderNo: String, bankId: String, type: Boolean) {
        emitData<DamiTagBean>(controller, request = { DamiApi.bankCheckData(orderNo, bankId) }, showToast = true) {
            if (type) {
                damiNavigate(context, controller, it.redirectUrl) {
                    controller.popBackStack()
                }
            } else {
                controller.popBackStack()
            }
        }
    }

    fun addressList(controller: NavHostController, toast: Boolean = true, onResult: (List<DamiAddressBean>?) -> Unit = {}) {
        DamiApplication.addressList?.also { onResult(it) } ?: emitData<DamiAddressBean>(controller, request = { DamiApi.address() }, loading = toast, errorToast = toast) {
            DamiApplication.addressList = it.list
            onResult(it.list)
        }
    }

    fun damiNavigate(context: Context, controller: NavHostController, url: String, msg: String? = null, callback: (() -> Unit)? = null, complete: () -> Unit = {}) {
        when {
            url.startsWith(DamiRouteUtils.DAMI_START) -> damiStart(context, controller, url.productId(), complete = complete)
            url.startsWith(DamiRouteUtils.DAMI_VERIFY) -> damiVerify(context, controller, url.productId(), complete)
            else -> {
                closeLoading()
                complete()
                DamiRouteUtils.navigate(controller, url) { callback?.invoke() ?: toast(msg ?: "") }
            }
        }
    }

    fun damiData(type: Int, id: String = "", orderNo: String = "", complete: (Boolean) -> Unit = {}) {
        flowEmit(request = { DamiApi.damiData(type, id, orderNo, startTime) }) {
            complete(it.code == 0)
            if (it.code != 0) closeLoading()
        }
    }

    private fun damiNext(controller: NavHostController, product: DamiProductBean, complete: () -> Unit) {
        startTime = System.currentTimeMillis()
        emitData<DamiStartBean>(controller = controller, request = { DamiApi.damiNext(product) }, complete = { if (!it) closeLoading() }, loading = false) { bean ->
            damiData(9, bean.url.productId(), bean.url.orderNo()) {
                closeLoading()
                complete()
                controller.navigate(DamiLoanConfirmRoute(bean.url.productId()))
            }
        }
    }

    fun damiVerify(context: Context, controller: NavHostController, id: String, complete: () -> Unit) {
        if (DamiDataUtils.isLogin(controller)) {
            showLoading()
            emitData<DamiProductBean>(
                controller = controller,
                request = { DamiApi.damiVerify(id) },
                complete = { if (!it) closeLoading() },
                loading = false
            ) {
                if (it.url.isEmpty() && it.result == 200) {
                    val verifyModel = it.verify?.find { child -> child.status == 0 }
                    it.productDetail?.let { product ->
                        if (verifyModel == null) {
                            damiNext(controller, product, complete)
                        } else {
                            closeLoading()
                            complete()
                            when (verifyModel.taskType) {
                                "forei_gner" -> controller.navigate(DamiEKYCRoute(product.id, product.orderNo))
                                "condu_ctor" -> controller.navigate(DamiIDCardRoute(product.id, product.orderNo))
                                "emerg_ency" -> controller.navigate(DamiPersonalRoute(product.id, product.orderNo))
                                "wisec_rack" -> controller.navigate(DamiJobRoute(product.id, product.orderNo))
                                "preju_dice" -> controller.navigate(DamiExtRoute(product.id, product.orderNo))
                                "encou_rage" -> controller.navigate(DamiBankRoute(product.id, product.orderNo))
                            }
                        }
                    }
                } else {
                    damiNavigate(context, controller, it.url, it.msg, complete = complete)
                }
            }
        }
    }

    open fun damiStart(context: Context, controller: NavHostController, id: String, type: Int = 0, complete: () -> Unit = {}) {
        if (DamiDataUtils.isLogin(controller)) {
            DamiRequestUtils.requestLocation(context) {
                if (it) {
                    showLoading()
                    DamiApplication.damiData()
                    emitData<DamiStartBean>(
                        controller,
                        request = { DamiApi.damiStart(id, type) },
                        complete = { if (!it) closeLoading() },
                        loading = false
                    ) { bean ->
                        if (bean.url.isEmpty() && bean.result == 200) {
                            damiVerify(context, controller, id, complete)
                        } else {
                            damiNavigate(context, controller, bean.url, bean.msg, complete = complete)
                        }
                    }
                }
            }
        }
    }

    fun uploadMarket(context: Context, key: String) {
        if (DamiDataUtils.getBoolData(key, true)) {
            viewModelScope.launch {
                flow { emit(DamiDeviceUtils.marketData(context)) }
                    .flowOn(Dispatchers.IO)
                    .catch { }
                    .collect { market ->
                        emitData<DamiUserBean>(request = { DamiApi.marketData(market) }) {
                            DamiDataUtils.putBoolData(key, false)
                            DamiDeviceUtils.initAdjust(context, it.adjust_token)
                        }
                    }
            }
        }
    }

    inline fun <reified T> emitData(
        controller: NavHostController,
        noinline request: suspend CoroutineScope.() -> DamiBean,
        noinline complete: (Boolean) -> Unit = {},
        loading: Boolean = true,
        showToast: Boolean = false,
        errorToast: Boolean = true,
        crossinline callback: suspend (T) -> Unit,
    ) {
        flowEmit(controller, request, complete, loading, showToast, errorToast) {
            callback(it.damiBean())
        }
    }

    inline fun <reified T> emitData(
        noinline request: suspend CoroutineScope.() -> DamiBean,
        noinline complete: (Boolean) -> Unit = {},
        crossinline callback: suspend (T) -> Unit,
    ) {
        flowEmit(request) {
            complete(it.code == 0)
            if (it.code == 0) callback(it.data.damiBean())
        }
    }

    fun flowEmit(
        controller: NavHostController,
        request: suspend CoroutineScope.() -> DamiBean,
        complete: (Boolean) -> Unit = {},
        loading: Boolean = true,
        showToast: Boolean = false,
        errorToast: Boolean = true,
        callback: suspend (String) -> Unit = {},
    ) {
        if (loading) showLoading()
        flowEmit(request) {
            complete(it.code == 0)
            if (it.code == 0||it.code == 10086) {
                if (showToast) toast(it.message)
                callback(it.data)
            } else {
                if (errorToast) toast(it.message)
                if (it.code == -2) {
                    DamiDataUtils.clear()
                    delay(500)
                    controller.navigate(DamiLoginRoute) {
                        popUpTo<DamiHomeRoute>()
                    }
                }
            }
            if (loading) closeLoading()
        }
    }

    fun flowEmit(request: suspend CoroutineScope.() -> DamiBean, callback: suspend (DamiBean) -> Unit = {}) {
        viewModelScope.launch {
            if (DamiUtils.httpEnable()) {
                flow { emit(request()) }
                    .flowOn(Dispatchers.IO)
                    .catch {
                        callback(
                            DamiBean(
                                message = when {
                                    it.toString().contains("Code=-1009") -> "Network connection failed.:-1009"
                                    it.toString().contains("Code=-1202") -> "Network connection failed.:-1202"
                                    it.toString().contains("Code=-1004") -> "Network connection failed.:-1004"
                                    it is NoTransformationFoundException -> "Network connection failed."
                                    it is HttpRequestTimeoutException -> "Connection timed out."
                                    else -> it.message ?: "Network connection failed."
                                }
                            )
                        )
                    }
                    .collect { callback(it) }
            } else {
                callback(DamiBean(message = "Network connection failed."))
            }
        }
    }


}