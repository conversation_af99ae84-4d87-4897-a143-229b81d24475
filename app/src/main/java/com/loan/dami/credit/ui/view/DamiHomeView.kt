package com.loan.dami.credit.ui.view

import android.app.Activity
import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.bean.DamiProductBean
import com.loan.dami.credit.config.DamiCoil
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiFillCoil
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiRouteUtils
import com.loan.dami.credit.config.DamiSizeText
import com.loan.dami.credit.config.DamiUtils.date
import com.loan.dami.credit.config.DamiUtils.orderNo
import com.loan.dami.credit.config.DamiUtils.productId
import com.loan.dami.credit.config.DamiView
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiHomeModel
import com.loan.dami.credit.ui.dialog.DamiAppDialog
import com.loan.dami.credit.ui.dialog.DamiDialog
import com.loan.dami.credit.ui.dialog.DamiImageDialog
import com.loan.dami.credit.ui.dialog.DamiPrivateDialog
import com.loan.dami.credit.ui.route.DamiBankListRoute
import com.loan.dami.credit.ui.route.DamiMineRoute
import com.loan.dami.credit.ui.route.DamiQualityRoute
import com.lt.compose_views.banner.Banner
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun DamiHomeView() {
    val context = LocalContext.current
    val controller = LocalDamiController.current
    val model = viewModel { DamiHomeModel() }
    val state by model.state.collectAsState()
    LaunchedEffect(model) {
        DamiApplication.topBar(Color(if (state.httpEnable) 0xff016b76 else 0xffffffff))
        model.init(context, controller)
        DamiApplication.reCreditCall = { model.getHome(controller) }
    }
    val refreshState = rememberPullRefreshState(refreshing = state.refresh, onRefresh = { model.getHome(controller) })

    if (state.permission) {
        DamiPrivateDialog { model.closePermission(context, controller, it) }
    }

    if (state.retryState) {
        DamiDialog(
            title = "Tips",
            content = "We’re sorry, the loan transfer failed. Please check your account details and transaction account restrictions. If you need help, please contact customer support.",
            left = "Cancel",
            right = "Confirm"
        ) { model.bankRetryData(context, controller, it) }
    }
    if (state.alertState) {
        state.alertBean?.let { alert ->
            if (alert.type == "1") {
                alert.dialog?.let { bean ->
                    DamiAppDialog(bean) {
                        model.closeAlert()
                        if (it == 1) {
                            try {
                                context.startActivity(Intent(Intent.ACTION_VIEW, bean.url.toUri()))
                            } catch (_: Exception) {
                            }
                        }
                    }
                }
            } else {
                alert.dialog?.let { bean ->
                    DamiImageDialog(bean) {
                        model.closeAlert()
                        if (it == 1) {
                            model.damiNavigate(context, controller, bean.url)
                        }
                    }
                }
            }
        }
    }

    var backState by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()
    BackHandler {
        if (backState) {
            (context as Activity).finish()
        } else {
            scope.launch {
                model.toast("Click again to exit the program")
                backState = true
                delay(1000)
                backState = false
            }
        }
    }
    DamiView(
        model = model,
        topBar = {
            AnimatedVisibility(state.httpEnable) {
                Row(modifier = Modifier.padding(start = 20.dp), verticalAlignment = Alignment.CenterVertically) {
                    DamiImage(modifier = Modifier.size(60.dp), id = R.mipmap.user_pic)
                    Column(
                        modifier = Modifier
                            .padding(horizontal = 8.dp)
                            .weight(1f)
                    ) {
                        Text(text = "Hey!", color = Color(0xfffcf9f5), fontSize = 20.sp, fontWeight = FontWeight.Bold)
                        Text(modifier = Modifier.padding(top = 4.dp), text = "Welcome to Dami Credit", color = Color(0xfffcf9f5), fontSize = 16.sp)
                    }
                    DamiImage(
                        modifier = Modifier
                            .clickDelay { DamiRouteUtils.loginWeb(controller, state.bean?.icon?.linkUrl) }
                            .size(66.dp, 50.dp), id = R.mipmap.icon_home_call)
                }
            }
        },
        topBarColor = if (state.httpEnable) Color(0xff016b76) else Color.Transparent,
        bottomBarColor = if (state.httpEnable) Color.White else Color.Transparent,
        bottomBar = {
            AnimatedVisibility(state.httpEnable) {
                Row {
                    Box(Modifier.weight(1f), contentAlignment = Alignment.Center) {
                        DamiImage(
                            modifier = Modifier
                                .padding(13.dp)
                                .size(60.dp, 23.dp), id = R.mipmap.icon_home_s
                        )
                    }
                    Box(Modifier.weight(1f), contentAlignment = Alignment.Center) {
                        DamiImage(
                            modifier = Modifier
                                .clickDelay { if (DamiDataUtils.isLogin(controller)) controller.navigate(DamiMineRoute(state.bean?.icon?.linkUrl)) }
                                .padding(13.dp)
                                .size(60.dp, 23.dp), id = R.mipmap.icon_mine
                        )
                    }
                }
            }
        }
    ) {
        AnimatedVisibility(state.httpEnable) {
            Box(
                modifier = Modifier
                    .padding(it)
                    .pullRefresh(refreshState)
            ) {
                Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
                    state.bean?.banner()?.let { DamiHomeBanner(it) { bean -> model.bannerClick(context, controller, bean) } }
                    Column(
                        modifier = Modifier
                            .background(Color(0xff016b76))
                            .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                            .padding(10.dp)
                    ) {
                        state.bean?.apply {
                            card()?.let { DamiHomeCard(it) { id -> model.damiStart(context, controller, id) } }
                            progressList()?.let {
                                DamiHomeProgress(it) { type, bean ->
                                    when (type) {
                                        3 -> controller.navigate(DamiBankListRoute(bean.url.productId(), bean.url.orderNo(), true))
                                        2 -> model.retryState(bean.order_no)
                                        else -> model.damiNavigate(context, controller, bean.url)
                                    }
                                }
                            }
                        }
                    }
                    state.bean?.productList()?.let {
                        if (it.isNotEmpty()) DamiImage(
                            modifier = Modifier
                                .padding(vertical = 5.dp)
                                .size(190.dp, 41.dp), id = R.mipmap.recommend
                        )
                        it.forEach { DamiHomeProduct(it) { id -> model.damiStart(context, controller, id) } }
                    }
                    DamiFillImage(modifier = Modifier.padding(horizontal = 35.dp), id = R.mipmap.home_tip)
                    DamiFillImage(
                        modifier = Modifier
                            .padding(horizontal = 20.dp, vertical = 12.dp)
                            .clickDelay { controller.navigate(DamiQualityRoute) }, id = R.mipmap.home_our
                    )
                }
                PullRefreshIndicator(modifier = Modifier.align(Alignment.TopCenter), refreshing = state.refresh, state = refreshState, contentColor = Color(0xff016b76))
            }
        }
        AnimatedVisibility(!state.httpEnable) {
            Column(modifier = Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center) {
                DamiFillImage(modifier = Modifier.padding(horizontal = 70.dp), id = R.mipmap.http_error)
                Text(
                    modifier = Modifier.padding(horizontal = 29.dp, vertical = 16.dp),
                    text = "Network error, please try again later or contact our customer service.",
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    color = Color(0xff6a6a6b),
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center
                )
                Text(
                    modifier = Modifier
                        .clip(CircleShape)
                        .border(1.dp, Color(0xff82c3bd), CircleShape)
                        .clickDelay { model.init(context, controller) }
                        .padding(horizontal = 29.dp, vertical = 12.dp)
                        .align(Alignment.CenterHorizontally),
                    text = "Try Again",
                    fontSize = 18.sp,
                    lineHeight = 25.sp,
                    color = Color(0xff016b76),
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}

@Composable
fun DamiHomeProduct(bean: DamiProductBean, call: (id: String) -> Unit) {
    Column(
        modifier = Modifier
            .padding(horizontal = 20.dp, vertical = 5.dp)
            .border(2.dp, color = Color(0xff82c3bd), shape = RoundedCornerShape(20.dp))
            .background(Color.White, shape = RoundedCornerShape(20.dp))
            .clickDelay(enable = bean.buttoncolor != "grey") { call(bean.id) }
            .padding(10.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Column(
                modifier = Modifier
                    .padding(end = 18.dp)
                    .width(61.dp)
                    .background(Color(0xfffdeedd), shape = RoundedCornerShape(10.dp))
                    .padding(vertical = 10.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DamiCoil(
                    modifier = Modifier
                        .padding(bottom = 6.dp)
                        .clip(RoundedCornerShape(5.dp))
                        .size(42.dp),
                    model = bean.productLogo
                )
                Text(text = bean.productName, fontSize = 10.sp, lineHeight = 12.sp, color = Color(0xff6a6a6b), fontWeight = FontWeight.Bold, textAlign = TextAlign.Center)
            }
            Column(Modifier.weight(1f)) {
                Text(text = bean.amountRangeDes, fontSize = 12.sp, lineHeight = 14.sp, color = Color(0xff6a6a6b))
                DamiSizeText(text = bean.amountRange, fontSize = 20.sp, lineHeight = 24.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Bold)
                Text(modifier = Modifier.padding(top = 8.dp, bottom = 2.dp), text = bean.loanTermText, fontSize = 12.sp, lineHeight = 14.sp, color = Color(0xff6a6a6b))
                Text(text = bean.termInfo, fontSize = 12.sp, lineHeight = 17.sp, color = Color(0xff2a292a), fontWeight = FontWeight.SemiBold)
            }
            Column {
                Text(
                    modifier = Modifier
                        .background(Color(if (bean.buttoncolor == "grey") 0xffe6e6e6 else if (bean.buttoncolor == "red") 0xfff43939 else 0xffff9000), shape = CircleShape)
                        .padding(horizontal = 15.dp, vertical = 10.dp),
                    text = bean.buttonText,
                    fontSize = 12.sp,
                    lineHeight = 14.sp,
                    color = Color(0xffffffff),
                    fontWeight = FontWeight.Bold
                )
                Text(modifier = Modifier.padding(top = 12.dp, bottom = 2.dp), text = bean.loanRateDes, fontSize = 12.sp, lineHeight = 14.sp, color = Color(0xff6a6a6b))
                Text(text = bean.loan_rate, fontSize = 12.sp, lineHeight = 17.sp, color = Color(0xff2a292a), fontWeight = FontWeight.SemiBold)
            }
        }
        HorizontalDivider(modifier = Modifier.padding(vertical = 10.dp), color = Color(0xffe7e7e7))
        Text(
            text = "Low interest rates / Ages 17 years and over can borrow",
            fontSize = 11.sp,
            lineHeight = 12.sp,
            color = Color(0xffec8944),
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun DamiHomeProgress(list: List<DamiProductBean>, call: (type: Int, bean: DamiProductBean) -> Unit) {
    Banner(pageCount = list.size, userEnable = list.size > 1, autoScroll = list.size > 1) {
        list[index].let { bean ->
            Column(
                modifier = Modifier
                    .padding(7.dp)
                    .shadow(5.dp, RoundedCornerShape(16.dp))
                    .background(brush = Brush.verticalGradient(bean.processBg()))
                    .clickDelay { call(1, bean) }
            ) {
                Row(modifier = Modifier.padding(top = 12.dp, start = 30.dp, end = 30.dp), verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        modifier = Modifier
                            .background(bean.processTitleBg(), shape = CircleShape)
                            .padding(horizontal = 8.dp, vertical = 4.dp),
                        text = bean.title,
                        fontSize = 10.sp,
                        lineHeight = 14.sp,
                        color = Color.White
                    )
                    if (bean.processTime() > 0) {
                        var time by remember { mutableLongStateOf(bean.processTime()) }
                        LaunchedEffect(time) {
                            if (time > 0) {
                                delay(1000)
                                time--
                            }
                        }
                        Text(
                            modifier = Modifier.weight(1f),
                            text = time.date(),
                            fontSize = 12.sp,
                            lineHeight = 14.sp,
                            color = Color(0xff016b76),
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.End
                        )
                    }
                }
                Column(
                    modifier = Modifier
                        .padding(horizontal = 30.dp, vertical = 8.dp)
                        .background(Color.White, shape = RoundedCornerShape(10.dp))
                        .padding(10.dp)
                ) {
                    Row(modifier = Modifier.padding(start = 10.dp)) {
                        Text(modifier = Modifier.weight(1f), text = bean.amount, fontSize = 14.sp, lineHeight = 20.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                        Text(modifier = Modifier.weight(1f), text = bean.date, fontSize = 14.sp, lineHeight = 20.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                    }
                    if (bean.status2 > 4) HorizontalDivider(modifier = Modifier.padding(10.dp), color = Color(0xffd1d1d1))
                    Row(modifier = Modifier.padding(start = 10.dp)) {
                        Text(modifier = Modifier.weight(1f), text = bean.amount_text, fontSize = 10.sp, lineHeight = 14.sp, color = Color(0xff6a6a6b), fontWeight = FontWeight.Medium)
                        Text(modifier = Modifier.weight(1f), text = bean.date_text, fontSize = 10.sp, lineHeight = 14.sp, color = Color(0xff6a6a6b), fontWeight = FontWeight.Medium)
                    }
                    if (bean.status2 == 1 || bean.status2 == 4) {
                        HorizontalDivider(modifier = Modifier.padding(10.dp), color = Color(0xffd1d1d1))
                        Row(modifier = Modifier.padding(start = 10.dp)) {
                            Text(modifier = Modifier.weight(1f), text = bean.account_text, fontSize = 10.sp, lineHeight = 14.sp, color = Color(0xff6a6a6b), fontWeight = FontWeight.Medium)
                            Text(modifier = Modifier.weight(1f), text = bean.account, fontSize = 12.sp, lineHeight = 14.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Bold)
                        }
                    }
                }
                if (bean.status2 > 4) {
                    Column(
                        modifier = Modifier
                            .padding(horizontal = 30.dp, vertical = 4.dp)
                            .background(Color.White, shape = RoundedCornerShape(10.dp))
                            .padding(10.dp)
                    ) {
                        Text(
                            modifier = Modifier.padding(start = 10.dp),
                            text = bean.account,
                            fontSize = 12.sp,
                            lineHeight = 14.sp,
                            color = Color(0xff2a292a),
                            fontWeight = FontWeight.Bold
                        )
                        HorizontalDivider(modifier = Modifier.padding(10.dp), color = Color(0xffd1d1d1))
                        Text(
                            modifier = Modifier.padding(start = 10.dp),
                            text = bean.account_text,
                            fontSize = 10.sp,
                            lineHeight = 14.sp,
                            color = Color(0xff6a6a6b),
                            fontWeight = FontWeight.Medium
                        )
                    }
                } else {
                    Text(
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
                        text = bean.loan_process_text,
                        fontSize = 11.sp,
                        lineHeight = 14.sp,
                        color = Color(0xff333333),
                        fontWeight = FontWeight.Bold
                    )
                    Column(
                        modifier = Modifier
                            .padding(horizontal = 12.dp, vertical = 4.dp)
                            .background(Color.White, shape = RoundedCornerShape(10.dp))
                            .padding(horizontal = 8.dp, vertical = 12.dp)
                    ) {
                        Box(contentAlignment = Alignment.Center) {
                            bean.loan_process_list?.let { list ->
                                val mList = list.filter { it.selected == 1 }
                                LinearProgressIndicator(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(Color(0xffe6e6e6), shape = CircleShape)
                                        .height(6.dp),
                                    progress = { (1.0 / list.size * mList.size).toFloat() },
                                    drawStopIndicator = {},
                                    trackColor = Color(0xffe6e6e6),
                                    gapSize = 0.dp,
                                    color = Color(0xff016b76)
                                )
                                Row {
                                    Spacer(Modifier.weight(.5f))
                                    list.forEachIndexed { i, b ->
                                        if (i != 0) Spacer(Modifier.weight(1f))
                                        DamiImage(modifier = Modifier.size(14.dp), id = if (b.selected == 1) R.mipmap.home_progress_s else R.mipmap.home_progress)
                                    }
                                    Spacer(Modifier.weight(.5f))
                                }
                            }
                        }
                        Row(modifier = Modifier.padding(top = 10.dp)) {
                            bean.loan_process_list?.forEach { process ->
                                DamiSizeText(modifier = Modifier.weight(1f), textAlign = TextAlign.Center, text = process.title, fontSize = 8.sp, lineHeight = 10.sp, color = Color(0xffb3b3b3))
                            }
                        }
                        Row(modifier = Modifier.padding(top = 10.dp)) {
                            bean.loan_process_list?.forEach { process ->
                                DamiSizeText(
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(horizontal = 5.dp)
                                        .background(Color(if (process.selected == 1) 0xff016b76 else 0xffe6e6e6), shape = RoundedCornerShape(4.dp))
                                        .padding(vertical = 5.dp),
                                    text = process.loan_amount,
                                    fontSize = 10.sp,
                                    lineHeight = 12.sp,
                                    color = Color(if (process.selected == 1) 0xffffffff else 0xffb3b3b3),
                                    fontWeight = FontWeight.Medium,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }

                if (bean.status2 > 4) {
                    Row(modifier = Modifier.padding(top = 8.dp)) {
                        bean.buttons?.filter { it.ifShow == 1 }?.let { list ->
                            list.forEachIndexed { i, b ->
                                DamiSizeText(
                                    modifier = Modifier
                                        .padding(end = if (list.size > 1 && i == 0) 10.dp else 0.dp, start = if (list.size > 1 && i == 1) 10.dp else 0.dp)
                                        .weight(1f)
                                        .shadow(5.dp, shape = RoundedCornerShape(topEnd = if (list.size > 1 && i == 0) 16.dp else 0.dp, topStart = if (list.size > 1 && i == 1) 16.dp else 0.dp))
                                        .background(brush = Brush.verticalGradient(listOf(Color(0xfffdfdfc), Color(0xfffef4ee), Color(0xfff6e3d7))))
                                        .clickDelay { call(if (b.type == "change") 3 else if (b.type == "retry") 2 else 1, bean) }
                                        .padding(11.dp),
                                    text = b.button_text,
                                    fontSize = 14.sp,
                                    lineHeight = 20.sp,
                                    color = Color(0xff999999),
                                    fontWeight = FontWeight.Medium,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                } else if (bean.status2 == 2 || bean.status2 == 3) {
                    DamiSizeText(
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .fillMaxWidth()
                            .shadow(5.dp)
                            .background(brush = Brush.verticalGradient(listOf(Color(0xfffdfdfc), Color(0xfffff5f5), Color(0xfff7e6e6))))
                            .padding(11.dp),
                        text = if (bean.status2 == 2) "Repay" else "Repay Now",
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        color = Color(0xff999999),
                        fontWeight = FontWeight.Medium,
                        textAlign = TextAlign.Center
                    )
                } else {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

@Composable
fun DamiHomeBanner(list: List<DamiProductBean>, call: (DamiProductBean) -> Unit) {
    Banner(
        modifier = Modifier
            .background(Color(0xff016b76))
            .padding(horizontal = 20.dp, vertical = 16.dp), pageCount = list.size, userEnable = list.size > 1, autoScroll = list.size > 1, autoScrollTime = 5000
    ) {
        list[index].let {
            DamiFillCoil(
                modifier = Modifier.clickDelay { call(it) },
                model = it.imgUrl,
                empty = R.mipmap.home_banner
            )
        }
    }
}

@Composable
fun DamiHomeCard(bean: DamiProductBean, call: (id: String) -> Unit) {
    Box(
        modifier = Modifier
            .padding(7.dp)
            .clickDelay { call(bean.id) }) {
        DamiFillImage(id = R.mipmap.home_card)
        Row(
            modifier = Modifier
                .padding(top = 8.dp, end = 38.dp)
                .background(Color(0xff016b76), shape = CircleShape)
                .padding(horizontal = 3.dp, vertical = 2.dp)
                .align(Alignment.TopEnd),
            verticalAlignment = Alignment.CenterVertically
        ) {
            DamiCoil(
                modifier = Modifier
                    .clip(CircleShape)
                    .size(18.dp), model = bean.productLogo
            )
            Text(modifier = Modifier.padding(start = 4.dp, end = 15.dp), text = bean.productName, fontSize = 10.sp, lineHeight = 12.sp, color = Color.White, fontWeight = FontWeight.Bold)
        }
        Column(modifier = Modifier.padding(horizontal = 45.dp, vertical = 24.dp)) {
            Text(modifier = Modifier.padding(start = 11.dp, end = 11.dp, top = 9.dp), text = bean.amountRangeDes, lineHeight = 18.sp, fontSize = 12.sp, color = Color(0xffca9a79))
            DamiSizeText(
                modifier = Modifier.padding(horizontal = 11.dp),
                text = bean.amountRange,
                lineHeight = 50.sp,
                fontSize = 44.sp,
                color = Color(0xff593223),
                fontWeight = FontWeight.Black
            )
            Row(
                modifier = Modifier
                    .padding(top = 8.dp)
                    .background(Color(0xfffff0ec), shape = RoundedCornerShape(8.dp))
                    .padding(10.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (bean.period_list.isNullOrEmpty()) {
                    Row(Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically) {
                        DamiImage(modifier = Modifier.size(20.dp), id = R.mipmap.home_term)
                        Column(modifier = Modifier.padding(start = 10.dp)) {
                            Text(text = bean.termInfo, fontSize = 12.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold, lineHeight = 14.sp)
                            Text(text = bean.termInfoDes, fontSize = 8.sp, color = Color(0xff999999), lineHeight = 10.sp)
                        }
                    }
                    VerticalDivider(modifier = Modifier.size(2.dp, 20.dp), color = Color.White)
                    Row(Modifier.weight(1f), horizontalArrangement = Arrangement.End, verticalAlignment = Alignment.CenterVertically) {
                        DamiImage(modifier = Modifier.size(20.dp), id = R.mipmap.home_rate)
                        Column(modifier = Modifier.padding(start = 10.dp)) {
                            Text(text = bean.loanRate, fontSize = 12.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold, lineHeight = 14.sp)
                            Text(text = bean.loanRateDes, fontSize = 8.sp, color = Color(0xff999999), lineHeight = 10.sp)
                        }
                    }
                } else {
                    bean.period_list?.forEachIndexed { i, period ->
                        if (i != 0) VerticalDivider(modifier = Modifier.size(2.dp, 20.dp), color = Color.White)
                        Row(Modifier.weight(1f), horizontalArrangement = if (i == 0) Arrangement.Start else Arrangement.End, verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                modifier = Modifier
                                    .background(Color(0xffffbd4b), CircleShape)
                                    .padding(horizontal = 5.dp, vertical = 2.dp),
                                text = period.period,
                                fontSize = 14.sp,
                                lineHeight = 14.sp,
                                color = Color.White,
                                fontWeight = FontWeight.ExtraBold
                            )
                            Column(modifier = Modifier.padding(start = 10.dp)) {
                                Text(text = period.loanRate, fontSize = 12.sp, color = Color(0xff333333), fontWeight = FontWeight.Bold, lineHeight = 14.sp)
                                Text(text = period.periodDesc, fontSize = 8.sp, color = Color(0xff999999), lineHeight = 10.sp)
                            }
                        }
                    }
                }
            }
        }
        Column(
            modifier = Modifier
                .padding(10.dp)
                .align(Alignment.BottomCenter),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(modifier = Modifier.padding(bottom = 35.dp), contentAlignment = Alignment.Center) {
                DamiImage(modifier = Modifier.size(226.dp, 44.dp), id = R.mipmap.home_card_btn)
                Text(text = bean.buttonText, fontSize = 22.sp, lineHeight = 22.sp, color = Color.White, fontWeight = FontWeight.ExtraBold)
            }
            Text(text = bean.loan_process_text, fontSize = 10.sp, lineHeight = 12.sp, color = Color(0xff713219), textAlign = TextAlign.Center)
        }
    }
}