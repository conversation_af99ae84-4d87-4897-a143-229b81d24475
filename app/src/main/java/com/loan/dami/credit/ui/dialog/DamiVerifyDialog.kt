package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.loan.dami.credit.config.DamiFillCoil

@Composable
fun DamiVerifyDialog(url: String, onCall: (Int) -> Unit) {
    Dialog(onDismissRequest = { onCall(0) }) {
        Box {
            DamiFillCoil(model = url, color = Color.Transparent)
            Row(modifier = Modifier.align(Alignment.BottomCenter), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    modifier = Modifier
                        .padding(start = 20.dp, bottom = 25.dp, end = 6.dp)
                        .weight(1f)
                        .clickable { onCall(1) }
                        .border(2.dp, Color.White, CircleShape)
                        .padding(vertical = 14.dp),
                    textAlign = TextAlign.Center,
                    text = "Exit",
                    fontSize = 18.sp,
                    lineHeight = 20.sp,
                    color = Color.White,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    modifier = Modifier
                        .padding(end = 20.dp, bottom = 25.dp, start = 6.dp)
                        .weight(1f)
                        .clickable { onCall(2) }
                        .background(brush = Brush.verticalGradient(listOf(Color.White, Color(0xffdeffda))), CircleShape)
                        .padding(vertical = 15.dp),
                    textAlign = TextAlign.Center,
                    text = "Continue",
                    fontSize = 18.sp,
                    lineHeight = 22.sp,
                    color = Color(0xff016b76),
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}