package com.loan.dami.credit.model

import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavHostController
import com.google.firebase.messaging.FirebaseMessaging
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiAlertBean
import com.loan.dami.credit.bean.DamiHomeBean
import com.loan.dami.credit.bean.DamiProductBean
import com.loan.dami.credit.bean.DamiTagBean
import com.loan.dami.credit.config.DamiDataUtils
import com.loan.dami.credit.config.DamiLocationUtils
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiRequestUtils
import com.loan.dami.credit.config.DamiRouteUtils
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.config.DamiUtils.httpEnable
import com.loan.dami.credit.config.DamiUtils.productId
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

data class DamiHomeState(
    val refresh: Boolean = false,
    val httpEnable: Boolean = httpEnable(),
    val permission: Boolean = false,
    val bean: DamiHomeBean? = null,
    val alertBean: DamiAlertBean? = null,
    val retryState: Boolean = false,
    val alertState: Boolean = false,
    val id: String? = null,
    val orderNo: String = ""
) : DamiState()

class DamiHomeModel : DamiModel<DamiHomeState>(DamiHomeState()) {

    override fun damiStart(context: Context, controller: NavHostController, id: String, type: Int, complete: () -> Unit) {
        if (DamiDataUtils.getBoolData(DamiDataUtils.PERMISSION, false)) {
            super.damiStart(context, controller, id, type, complete)
        } else {
            setData(data.copy(permission = true, id = id))
        }
    }

    fun closePermission(context: Context, controller: NavHostController, type: Int) {
        if (type == 1) {
            DamiDataUtils.putBoolData(DamiDataUtils.PERMISSION, true)
            DamiRequestUtils.requestLocation(context) {
                viewModelScope.launch(Dispatchers.IO) {
                    DamiLocationUtils.location(context)
                }
                data.id?.let { damiStart(context, controller, it) }
            }
        }
        setData(data.copy(permission = false))
    }

    fun init(context: Context, controller: NavHostController) {
        DamiApplication.pushCall = { url -> setPush(context, controller, url) }
        DamiApplication.pushUrl?.let { url -> setPush(context, controller, url) }
        getHome(controller)
        notification(context)
        if (DamiDataUtils.isLogin()) {
            uploadMarket(context, DamiDataUtils.LOGIN_MARKET)
        }
    }

    fun setPush(context: Context, controller: NavHostController, url: String) {
        DamiApplication.pushUrl = null
        viewModelScope.launch {
            if (url.startsWith(DamiRouteUtils.DAMI_START)) {
                damiStart(context, controller, url.productId(), type = 3)
            } else {
                damiNavigate(context, controller, url)
            }
        }
    }

    fun getHome(controller: NavHostController) {
        setData(data.copy(refresh = true))
        emitData<DamiHomeBean>(controller, request = { DamiApi.getHome() }, complete = { setData(data.copy(refresh = false, httpEnable = httpEnable())) }) {
            setData(data.copy(bean = it, httpEnable = httpEnable()))
            alert(1) { setData(data.copy(alertBean = it, alertState = it.dialog != null)) }
        }
    }

    fun closeAlert() {
        setData(data.copy(alertState = false))
    }

    fun retryState(orderNo: String) {
        setData(data.copy(retryState = true, orderNo = orderNo))
    }

    fun bankRetryData(context: Context, controller: NavHostController, type: Int) {
        setData(data.copy(retryState = false))
        if (type == 2) {
            showLoading()
            emitData<DamiTagBean>(controller, request = { DamiApi.bankRetryData(data.orderNo) }, loading = false, complete = { if (!it) closeLoading() }) {
                damiNavigate(context, controller, it.redirectUrl)
            }
        }
    }

    private fun notification(context: Context) {
        try {
            FirebaseMessaging.getInstance().token.addOnCompleteListener {
                try {
                    val token = it.result
                    flowEmit(request = { DamiApi.notification(token) }) {
                        DamiRequestUtils.requestNotification(context)
                    }
                } catch (_: Exception) {
                }
            }
        } catch (_: Exception) {
        }
    }

    fun bannerClick(context: Context, controller: NavHostController, bean: DamiProductBean) {
        if (bean.url.startsWith(DamiRouteUtils.DAMI_START)) {
            damiStart(context, controller, bean.url.productId(), type = 1)
        } else {
            damiNavigate(context, controller, bean.url, callback = {
                try {
                    context.startActivity(Intent(Intent.ACTION_VIEW, bean.url.toUri()))
                } catch (_: Exception) {
                }
            })
        }
        if (DamiDataUtils.isLogin() && bean.url.isNotEmpty()) {
            flowEmit(request = { DamiApi.bannerData(bean.id) })
        }
    }
}