package com.loan.dami.credit

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import androidx.compose.ui.graphics.Color
import coil3.ImageLoader
import coil3.PlatformContext
import coil3.SingletonImageLoader
import coil3.request.addLastModifiedToFileCacheKey
import com.loan.dami.credit.bean.DamiAddressBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import java.io.File
import java.io.FileOutputStream

class DamiApplication : Application(), SingletonImageLoader.Factory {

    companion object {
        lateinit var app: Application
        var key = "33aebd34cb63cf63"
        var vi = "4f1226bac585de8d"
        var secretKey = "abfdaca0f66383e9e504c27fa85373b0"
        var googleId = ""
        var facePath = ""
        var pushUrl: String? = null
        var pushCall: ((String) -> Unit)? = null
        var camearCall: (String) -> Unit = {}
        var damiData = {}
        var reCredit = {}
        var reCreditCall: (() -> Unit) = {}
        var addressList: List<DamiAddressBean>? = null
        var topBar: (Color) -> Unit = {}
    }

    override fun onCreate() {
        app = this
        super.onCreate()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            (getSystemService(NOTIFICATION_SERVICE) as NotificationManager).createNotificationChannel(NotificationChannel("dami_credit", "dami_credit", NotificationManager.IMPORTANCE_HIGH).apply {
                description = "dami_notification"
                enableLights(true)
                lightColor = android.graphics.Color.RED
                enableVibration(true)
            })
        }

        runBlocking(Dispatchers.IO) {
            try {
                val content = resources.openRawResource(R.raw.face)
                val mPackage = File("${filesDir.path}/face").apply {
                    if (!exists()) mkdirs()
                }
                val file = File(mPackage, "face.bin")
                FileOutputStream(file).use { output ->
                    content.copyTo(output)
                }
                facePath = file.absolutePath
            } catch (_: Exception) {
            }
        }

    }

    override fun newImageLoader(context: PlatformContext): ImageLoader {
        return ImageLoader.Builder(applicationContext).addLastModifiedToFileCacheKey(true).build()
    }
}