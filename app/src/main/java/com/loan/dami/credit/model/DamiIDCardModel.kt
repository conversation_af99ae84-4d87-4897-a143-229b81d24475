package com.loan.dami.credit.model

import android.content.Context
import androidx.navigation.NavHostController
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiEKYCBean
import com.loan.dami.credit.bean.DamiIDCardBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiRequestUtils
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiIDCardRoute
import com.megvii.meglive_sdk.listener.PreCallback
import com.megvii.meglive_sdk.manager.MegLiveManager

data class DamiIDCardState(val errorState: Boolean = false) : DamiState()

class DamiIDCardModel(private val route: DamiIDCardRoute) : DamiModel<DamiIDCardState>(DamiIDCardState()) {

    fun closeError(){
        setData(data.copy(errorState = false))
    }

    fun idCardData(context: Context, controller: NavHostController) {
        startTime = System.currentTimeMillis()
        DamiRequestUtils.requestCamera(context) {
            if (it) {
                showLoading()
                emitData<DamiIDCardBean>(controller, request = { DamiApi.idCardData(route.orderNo) }, loading = false, complete = { if (!it) closeLoading() }) {
                    if (it.result_code == "200" && it.biz_token.isNotEmpty()) {
                        val manager = MegLiveManager.getInstance()
                        manager.preDetect(context, it.biz_token, "", it.biz_url, DamiApplication.facePath, object : PreCallback {

                            override fun onPreStart() {}

                            override fun onPreFinish(p0: String?, p1: Int, p2: String?) {
                                if (p1 == 1000) {
                                    manager.setVerticalDetectionType(MegLiveManager.DETECT_VERITICAL_FRONT)
                                    manager.startDetect { bizToken, code, msg, data ->
                                        if (code == 1000 && bizToken.isNotEmpty() && data.isNotEmpty()) {
                                            uploadFile(context, controller, data, bizToken)
                                        } else {
                                            closeLoading()
                                            toast(msg ?: "error")
                                        }
                                    }
                                } else {
                                    closeLoading()
                                    toast(p2 ?: "error")
                                }
                            }
                        })
                    } else if (it.result_code == "400") {
                        closeLoading()
                        setData(data.copy(errorState = true))
                    } else {
                        closeLoading()
                        toast(it.error)
                    }
                }
            }
        }
    }

    fun uploadFile(context: Context, controller: NavHostController, path: String, bizToken: String) {
        emitData<DamiEKYCBean>(controller, request = { DamiApi.uploadFile(10, 1, path, "", "5", bizToken) }, loading = false, complete = { if (!it) closeLoading() }) {
            damiData(4, route.id, route.orderNo) {
                damiVerify(context, controller, route.id) {
                    controller.popBackStack()
                }
            }
        }
    }
}