package com.loan.dami.credit.config

import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.location.LocationManager
import android.os.Build
import android.provider.Settings
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.OnPermissionPageCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions

object DamiRequestUtils {

    fun requestNotification(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!XXPermissions.isGranted(context, Permission.POST_NOTIFICATIONS)) {
                XXPermissions.with(context)
                    .permission(Permission.POST_NOTIFICATIONS)
                    .request(null)
            }
        }
    }

    fun requestLocation(context: Context, onCallback: (Boolean) -> Unit) {
        if (locationEnable(context)) {
            if (grantedLocation(context)) {
                onCallback(true)
            } else {
                XXPermissions.with(context)
                    .permission(Permission.ACCESS_COARSE_LOCATION)
                    .request(object : OnPermissionCallback {
                        override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                            if (allGranted) {
                                onCallback(true)
                            }
                        }

                        override fun onDenied(permissions: MutableList<String>, doNotAskAgain: Boolean) {
                            AlertDialog
                                .Builder(context)
                                .setTitle("Tips")
                                .setMessage("Location access is disabled. Some features may be limited. Enable it in settings for full functionality.")
                                .setOnCancelListener { onCallback(false) }
                                .setNeutralButton("Cancel") { _, _ -> onCallback(false) }
                                .setNegativeButton("Go to Settings") { _, _ ->
                                    XXPermissions.startPermissionActivity(context as Activity, Permission.ACCESS_COARSE_LOCATION, object : OnPermissionPageCallback {
                                        override fun onGranted() {
                                            onCallback(true)
                                        }

                                        override fun onDenied() {
                                            onCallback(false)
                                        }
                                    })
                                }
                                .show()
                        }
                    })
            }
        } else {
            AlertDialog
                .Builder(context)
                .setTitle("Tips")
                .setMessage("Your device’s location services are turned off. Turn them on to use location-based features.")
                .setOnCancelListener { onCallback(false) }
                .setNegativeButton("Cancel") { _, _ -> onCallback(false) }
                .setPositiveButton("Enable Now") { _, _ -> context.startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)) }
                .show()
        }
    }

    fun grantedLocation(context: Context) = XXPermissions.isGranted(context, Permission.ACCESS_COARSE_LOCATION)

    fun locationEnable(context: Context): Boolean {
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val mode = Settings.Secure.getInt(context.contentResolver, Settings.Secure.LOCATION_MODE, Settings.Secure.LOCATION_MODE_OFF)
            mode != Settings.Secure.LOCATION_MODE_OFF
        } else {
            locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        }
    }

    fun requestCamera(context: Context, onCallback: (Boolean) -> Unit) {
        if (XXPermissions.isGranted(context, Permission.CAMERA)) {
            onCallback(true)
        } else {
            XXPermissions.with(context)
                .permission(Permission.CAMERA)
                .request(object : OnPermissionCallback {
                    override fun onGranted(permissions: MutableList<String>, allGranted: Boolean) {
                        if (allGranted) {
                            onCallback(true)
                        }
                    }

                    override fun onDenied(permissions: MutableList<String>, doNotAskAgain: Boolean) {
                        AlertDialog
                            .Builder(context)
                            .setTitle("Tips")
                            .setMessage("Camera access is required for identity verification. Enable permissions in settings to proceed.")
                            .setOnCancelListener { onCallback(false) }
                            .setNeutralButton("Cancel") { _, _ -> onCallback(false) }
                            .setNegativeButton("Go to Settings") { _, _ ->
                                XXPermissions.startPermissionActivity(context as Activity, Permission.CAMERA, object : OnPermissionPageCallback {
                                    override fun onGranted() {
                                        onCallback(true)
                                    }

                                    override fun onDenied() {
                                        onCallback(false)
                                    }
                                })
                            }
                            .show()
                    }
                })
        }
    }
}