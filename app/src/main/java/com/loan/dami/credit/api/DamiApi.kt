package com.loan.dami.credit.api

import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.api.DamiClient.httpClient
import com.loan.dami.credit.bean.DamiBean
import com.loan.dami.credit.bean.DamiLoanConfirmBean
import com.loan.dami.credit.bean.DamiProductBean
import com.loan.dami.credit.config.DamiLocationUtils
import com.loan.dami.credit.config.DamiUtils.buildDami
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import kotlinx.serialization.json.JsonObjectBuilder
import kotlinx.serialization.json.put

object DamiApi {

    suspend fun getHome() = httpClient.get("oryfact").body<DamiBean>()

    suspend fun reCredit() = httpClient.get("ertdes") {
        setBody(buildDami { put("donaban", "ertdes") })
    }.body<DamiBean>()

    suspend fun alert(type: Int) = httpClient.get("ionintent") {
        setBody(buildDami { put("orefolkl", type) })
    }.body<DamiBean>()

    suspend fun address() = httpClient.get("hipcensors").body<DamiBean>()

    suspend fun loginCode(number: String) = httpClient.post("laxearthf") {
        setBody(buildDami {
            put("laxearthf", number)
            put("ortdep", "laxearthf")
        })
    }.body<DamiBean>()

    suspend fun startLogin(number: String, code: String) = httpClient.post("ortdep") {
        setBody(buildDami {
            put("iveeffect", number)
            put("ertdes", code)
            put("ionintent", "iveeffect")
            put("ceroffi", "ertdes")
        })
    }.body<DamiBean>()

    suspend fun damiStart(id: String, type: Int) = httpClient.post("anaban") {
        setBody(buildDami {
            put("eamcr", id)
            put("ousconsci", type)
            put("tercen", "1001")
            put("rcereinfo", "1000")
            put("iontract", "1000")
            put("gletrian", "eamcr")
            put("bletrou", "ousconsci")
        })
    }.body<DamiBean>()

    suspend fun damiVerify(id: String) = httpClient.post("intconstra") {
        setBody(buildDami {
            put("eamcr", id)
            put("caphandi", "eamcr")
            put("ifyclass", "caphandi")
            put("intdisappo", "ifyclass")
        })
    }.body<DamiBean>()

    suspend fun damiNext(bean: DamiProductBean) = httpClient.post("ashsm") {
        setBody(buildDami {
            put("undref", bean.orderNo)
            put("larsecu", bean.amount)
            put("allst", bean.term)
            put("ghtti", bean.term_type)
            put("nelpa", "ghtti")
            put("serla", "nelpa")
            put("dalscan", "serla")
            put("akesh", "dalscan")
        })
    }.body<DamiBean>()

    suspend fun damiData(type: Int, id: String, orderNo: String, time: Long) = httpClient.post("entimprovem") {
        setBody(buildDami {
            put("lsefa", id)
            put("aryannivers", type)
            put("eadthr", orderNo)
            put("asyfant", DamiApplication.googleId)
            put("styta", DamiLocationUtils.lng)
            put("ectdial", DamiLocationUtils.lat)
            put("ucered", time / 1000)
            put("iumstad", System.currentTimeMillis() / 1000)
            put("agediscour", "aryannivers")
        })
    }.body<DamiBean>()

    suspend fun notification(key: String) = httpClient.post("nceexperie") {
        setBody(buildDami { put("airaff", key) })
    }.body<DamiBean>()

    suspend fun logout() = httpClient.get("urelect") {
        setBody(buildDami {
            put("inediscipl", "urepremat")
            put("urepremat", "inediscipl")
        })
    }.body<DamiBean>()

    suspend fun logoff() = httpClient.get("ateperfor") {
        setBody(buildDami {
            put("iffsn", "ateperfor")
        })
    }.body<DamiBean>()

    suspend fun loanList(type: Int, page: Int) = httpClient.post("bleincredi") {
        setBody(buildDami {
            put("ignres", type)
            put("athde", page)
            put("oveimpr", 10)
        })
    }.body<DamiBean>()

    suspend fun kycData(id: String) = httpClient.get("oselo") {
        setBody(buildDami {
            put("eamcr", id)
            put("itycommun", "eamcr")
        })
    }.body<DamiBean>()

    suspend fun uploadFile(type: Int, imageSource: Int, path: String, cardType: String, faceType: String = "", bizToken: String? = null) = httpClient.patch("ralneut") {
        setBody(buildDami {
            put("antten", type)
            put("ngeora", imageSource)
            put(if (faceType == "5") "face" else "attach", path)
            put("ishpubl", cardType)
            bizToken?.let { put("tryfores", it) }
            put("monle", faceType)
        })
    }.body<DamiBean>()

    suspend fun submitEKYCData(birthday: String, idNo: String, name: String, type: String) = httpClient.post("inediscipl") {
        setBody(buildDami {
            put("ineunderm", birthday)
            put("reerefe", idNo)
            put("aicmos", name)
            put("antten", "11")
            put("ishpubl", type)
            put("erypott", "ineunderm")
        })
    }.body<DamiBean>()

    suspend fun idCardData(id: String) = httpClient.post("iffsn") {
        setBody(buildDami {
            put("undref", id)
            put("sincou", "undref")
            put("ersleftov", "sincou")
        })
    }.body<DamiBean>()

    suspend fun personalData(id: String) = httpClient.post("ketjac") {
        setBody(buildDami {
            put("eamcr", id)
            put("ingfl", "eamcr")
        })
    }.body<DamiBean>()

    suspend fun savePersonalData(builder: JsonObjectBuilder.() -> Unit) = httpClient.post("ralmo") {
        setBody(buildDami(builder))
    }.body<DamiBean>()


    suspend fun jobData(id: String) = httpClient.post("tordemonstra") {
        setBody(buildDami {
            put("eamcr", id)
            put("ingfl", "eamcr")
        })
    }.body<DamiBean>()

    suspend fun saveJobData(builder: JsonObjectBuilder.() -> Unit) = httpClient.post("ateelabor") {
        setBody(buildDami(builder))
    }.body<DamiBean>()


    suspend fun extData(id: String) = httpClient.post("nceaudie") {
        setBody(buildDami {
            put("eamcr", id)
            put("ncebrillia", "eamcr")
        })
    }.body<DamiBean>()

    suspend fun saveExtData(builder: JsonObjectBuilder.() -> Unit) = httpClient.post("thmrhy") {
        setBody(buildDami(builder))
    }.body<DamiBean>()


    suspend fun bankData(id: String) = httpClient.post("antten") {
        setBody(buildDami {
            put("eamcr", id)
            put("ingfl", "eamcr")
        })
    }.body<DamiBean>()

    suspend fun saveBankData(builder: JsonObjectBuilder.() -> Unit) = httpClient.post("sonsalesper") {
        setBody(buildDami(builder))
    }.body<DamiBean>()

    suspend fun loanConfirmData(id: String, type: Int? = null, amount: String? = null) = httpClient.post("ionvers") {
        setBody(buildDami {
            put("lsefa", id)
            type?.let { put("ontconfr", it) }
            amount?.let { put("ionconfrontat", it) }
            put("ceroffi", "lsefa")
        })
    }.body<DamiBean>()

    suspend fun loanConfirm(id: String, bean: DamiLoanConfirmBean?) = httpClient.post("ionmutat") {
        setBody(buildDami {
            put("larsecu", bean?.amount)
            put("endext", 1)
            put("actextr", bean?.term)
            put("lsefa", id)
            put("ontconfr", bean?.actual_period_type)
            put("acemispl", "larsecu")
        })
    }.body<DamiBean>()

    suspend fun dialog(type: Int, id: String) = httpClient.post("glewrig") {
        setBody(buildDami {
            put("enddef", type)
            put("lsefa", id)
            put("uregest", "enddef")
        })
    }.body<DamiBean>()

    suspend fun bankListData(id: String) = httpClient.post("anddem") {
        setBody(buildDami {
            put("eamcr", id)
            put("nalconventio", "eamcr")
            put("nchlu", "nalconventio")
        })
    }.body<DamiBean>()

    suspend fun bankCheckData(orderNo: String, id: String) = httpClient.post("delmo") {
        setBody(buildDami {
            put("undref", orderNo)
            put("itypossibil", id)
            put("zeechimpan", "itypossibil")
        })
    }.body<DamiBean>()

    suspend fun bankRetryData(orderNo: String) = httpClient.post("ilemob") {
        setBody(buildDami {
            put("eadthr", orderNo)
        })
    }.body<DamiBean>()

    suspend fun bannerData(id: String) = httpClient.post("ceroffi") {
        setBody(buildDami {
            put("obewardr", id)
            put("lowpil", "obewardr")
        })
    }.body<DamiBean>()

    suspend fun locationData(json: String?) = httpClient.put("putout") { setBody(json) }.body<DamiBean>()
    suspend fun loanRateDes(json: String) = httpClient.put("omyecon") { setBody(json) }.body<DamiBean>()
    suspend fun termInfoDes(json: String) = httpClient.put("ionreproduct") { setBody(json) }.body<DamiBean>()
    suspend fun marketData(json: String) = httpClient.put("uremeas") { setBody(json) }.body<DamiBean>()
}
