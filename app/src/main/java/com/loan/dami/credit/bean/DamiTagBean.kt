package com.loan.dami.credit.bean

import kotlinx.serialization.Serializable

@Serializable
data class DamiTagBean(
    var bold: Int = 0,
    var status: Int = 0,
    var tag2: DamiTagBean? = null,
    var tag1: DamiTagBean? = null,
    var note: List<DamiTagBean>? = null,
    var text: String = "",
    var title: String = "",
    var logo: String? = null,
    var value: String = "",
    var live_time_type: String = "",
    var marriage: String = "",
    var degrees: String = "",
    var name: String = "",
    var type: String = "",
    var contract_pos: String = "",
    var contract_url: String = "",
    var loan_amount: String = "",
    var redirectUrl: String = "",
    var selected: Int = -1)
