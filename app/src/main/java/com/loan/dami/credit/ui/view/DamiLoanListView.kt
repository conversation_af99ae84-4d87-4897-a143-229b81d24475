package com.loan.dami.credit.ui.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiCoil
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiSizeText
import com.loan.dami.credit.config.DamiView
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiLoanListModel
import com.loan.dami.credit.ui.route.DamiLoanListRoute
import com.lt.compose_views.refresh_layout.RefreshLayout
import com.lt.compose_views.refresh_layout.rememberRefreshLayoutState
import com.lt.compose_views.util.ComposePosition

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun DamiLoanListView(route: DamiLoanListRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current
    val titles = remember { listOf("All", "Outstanding", "Overdue", "Settled") }
    val model = viewModel { DamiLoanListModel(route) }
    val state by model.state.collectAsState()

    val refreshState = rememberPullRefreshState(refreshing = state.refresh, onRefresh = { model.refreshList(controller, state.type) })

    val loadState = rememberRefreshLayoutState { model.loadList(controller, state.type, this) }

    LaunchedEffect(model) { model.refreshList(controller, state.type) }

    DamiView(model, title = "Loan List", onBack = { controller.popBackStack(route, true) }) {
        Column(
            modifier = Modifier
                .padding(it)
                .background(Color(0xff016b76))
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
        ) {
            Row(
                modifier = Modifier
                    .padding(top = 24.dp)
                    .background(Color(0xffdcebf4))
                    .padding(5.dp)
            ) {
                titles.forEachIndexed { i, title ->
                    DamiSizeText(
                        modifier = Modifier
                            .weight(1f)
                            .padding(4.dp)
                            .shadow(5.dp, shape = RoundedCornerShape(10.dp))
                            .background(Color.White)
                            .clickDelay { model.refreshList(controller, i) }
                            .padding(horizontal = 5.dp, vertical = 10.dp),
                        text = title,
                        fontSize = 16.sp,
                        lineHeight = 20.sp,
                        color = Color(if (i == state.type) 0xff016b76 else 0xffb0b0b0),
                        textAlign = TextAlign.Center
                    )
                }
            }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .pullRefresh(refreshState)
            ) {
                if (state.list.isEmpty()) {
                    Column(modifier = Modifier.padding(top = 50.dp), horizontalAlignment = Alignment.CenterHorizontally) {
                        DamiFillImage(id = R.mipmap.loan_list_empty, modifier = Modifier.padding(horizontal = 70.dp))
                        Text(modifier = Modifier.padding(top = 16.dp), text = "No information available", color = Color(0xffb0b0b0), fontSize = 14.sp, lineHeight = 18.sp)
                    }
                } else {
                    RefreshLayout(
                        refreshLayoutState = loadState,
                        composePosition = ComposePosition.Bottom,
                        refreshContent = {
                            Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                                CircularProgressIndicator(modifier = Modifier.size(26.dp), color = Color(0xff016b76), strokeWidth = 3.dp)
                            }
                        }
                    ) {
                        LazyColumn(modifier = Modifier.padding(horizontal = 14.dp, vertical = 4.dp)) {
                            items(items = state.list) { bean ->
                                Box(
                                    modifier = Modifier
                                        .padding(6.dp)
                                        .clickDelay {
                                            if (bean.loanDetailUrl.isEmpty()) {
                                                model.damiStart(context, controller, bean.productId)
                                            } else {
                                                model.damiNavigate(context, controller, bean.loanDetailUrl)
                                            }
                                        }
                                ) {
                                    DamiFillImage(id = bean.getItemBg())
                                    Column(modifier = Modifier.padding(20.dp)) {
                                        Row(verticalAlignment = Alignment.CenterVertically) {
                                            DamiCoil(
                                                modifier = Modifier
                                                    .clip(RoundedCornerShape(4.dp))
                                                    .size(28.dp), model = bean.productLogo
                                            )
                                            Text(
                                                modifier = Modifier.padding(horizontal = 8.dp),
                                                text = bean.productName,
                                                fontSize = 14.sp,
                                                lineHeight = 18.sp,
                                                color = Color(0xff29292a),
                                                fontWeight = FontWeight.Medium
                                            )
                                            DamiSizeText(
                                                modifier = Modifier.weight(1f),
                                                text = bean.orderStatusDesc,
                                                fontSize = 12.sp,
                                                lineHeight = 18.sp,
                                                color = bean.getItemColor(),
                                                textAlign = TextAlign.End
                                            )
                                        }
                                        Row(modifier = Modifier.padding(top = 16.dp)) {
                                            Text(modifier = Modifier.weight(1f), text = bean.moneyText, fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff6a6a6b))
                                            Text(text = bean.dateText, fontSize = 14.sp, lineHeight = 16.sp, color = Color(0xff6a6a6b))
                                        }
                                        Row(modifier = Modifier.padding(top = 2.dp)) {
                                            Text(
                                                modifier = Modifier.weight(1f),
                                                text = bean.orderAmount,
                                                fontSize = 18.sp,
                                                lineHeight = 20.sp,
                                                color = Color(0xff29292a),
                                                fontWeight = FontWeight.Medium
                                            )
                                            Text(
                                                text = if (bean.orderStatus == "180") bean.repayTime else bean.dateValue,
                                                fontSize = 18.sp,
                                                lineHeight = 20.sp,
                                                color = Color(0xff29292a),
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                        if (bean.showBtn()) Text(
                                            modifier = Modifier
                                                .padding(top = 16.dp)
                                                .background(bean.getItemColor(), shape = CircleShape)
                                                .padding(horizontal = 26.dp, vertical = 9.dp),
                                            text = bean.buttonText,
                                            fontSize = 16.sp,
                                            lineHeight = 20.sp,
                                            color = Color.White,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                PullRefreshIndicator(modifier = Modifier.align(Alignment.TopCenter), refreshing = state.refresh, state = refreshState, contentColor = Color(0xff016b76))
            }

        }
    }
}