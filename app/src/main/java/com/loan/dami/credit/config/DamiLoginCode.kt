package com.loan.dami.credit.config

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.Status

class DamiLoginCode : BroadcastReceiver() {

    companion object {
        var onCall: ((Intent) -> Unit)? = null
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        try {
            onCall?.let { call ->
                if (SmsRetriever.SMS_RETRIEVED_ACTION == intent?.action) {
                    intent.extras?.let { bundle ->
                        bundle.getCodeData(SmsRetriever.EXTRA_STATUS, Status::class.java)?.also { status ->
                            if (status.statusCode == CommonStatusCodes.SUCCESS) {
                                bundle.getCodeData(SmsRetriever.EXTRA_CONSENT_INTENT, Intent::class.java)?.also { intent ->
                                    call.invoke(intent)
                                }
                            }
                        }
                    }
                }
            }
        } catch (_: Exception) {
        }
    }

    fun <T : Parcelable> Bundle.getCodeData(name: String, clazz: Class<T>) = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
        getParcelable(name)
    } else {
        getParcelable(name, clazz)
    }
}