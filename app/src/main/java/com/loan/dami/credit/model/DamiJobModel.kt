package com.loan.dami.credit.model

import android.content.Context
import androidx.navigation.NavHostController
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiPersonalBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.ui.route.DamiJobRoute
import com.loan.dami.credit.ui.route.DamiPersonalRoute
import kotlinx.serialization.json.put

data class DamiJobState(val bean: DamiPersonalBean? = null) : DamiState()

class DamiJobModel(private val route: DamiJobRoute) : DamiModel<DamiJobState>(DamiJobState()) {

    fun jobData(controller: NavHostController) {
        emitData<DamiPersonalBean>(controller, request = { DamiApi.jobData(route.id) }) {
            setData(data.copy(bean = it))
        }
        addressList(controller, false)
    }

    fun saveJobData(context: Context, controller: NavHostController) {
        showLoading()
        flowEmit(controller, request = {
            DamiApi.saveJobData {
                put("eamcr", route.id)
                put("ghtthou", "eamcr")
                put("urepast", "ghtthou")
                put("redti", "urepast")
                data.bean?.items?.forEach { put(it.code, it.getText()) }
            }
        }, loading = false, complete = { if (!it) closeLoading() }) {
            damiData(6, route.id, route.orderNo) {
                damiVerify(context, controller, route.id) { controller.popBackStack() }
            }
        }
    }
}