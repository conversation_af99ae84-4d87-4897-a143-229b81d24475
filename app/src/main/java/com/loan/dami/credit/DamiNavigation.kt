package com.loan.dami.credit

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import com.loan.dami.credit.ui.route.DamiBankListRoute
import com.loan.dami.credit.ui.route.DamiBankRoute
import com.loan.dami.credit.ui.route.DamiCameraRoute
import com.loan.dami.credit.ui.route.DamiEKYCRoute
import com.loan.dami.credit.ui.route.DamiExtRoute
import com.loan.dami.credit.ui.route.DamiHomeRoute
import com.loan.dami.credit.ui.route.DamiIDCardRoute
import com.loan.dami.credit.ui.route.DamiJobRoute
import com.loan.dami.credit.ui.route.DamiLoanConfirmRoute
import com.loan.dami.credit.ui.route.DamiLoanListRoute
import com.loan.dami.credit.ui.route.DamiLoginRoute
import com.loan.dami.credit.ui.route.DamiMineRoute
import com.loan.dami.credit.ui.route.DamiPersonalRoute
import com.loan.dami.credit.ui.route.DamiPrivacyRoute
import com.loan.dami.credit.ui.route.DamiQualityRoute
import com.loan.dami.credit.ui.route.DamiRecreditRoute
import com.loan.dami.credit.ui.route.DamiSettingRoute
import com.loan.dami.credit.ui.route.DamiStartRoute
import com.loan.dami.credit.ui.route.DamiUrlRoute
import com.loan.dami.credit.ui.theme.DamiDreditTheme
import com.loan.dami.credit.ui.view.DamiBankListView
import com.loan.dami.credit.ui.view.DamiBankView
import com.loan.dami.credit.ui.view.DamiCameraView
import com.loan.dami.credit.ui.view.DamiEKYCView
import com.loan.dami.credit.ui.view.DamiExtView
import com.loan.dami.credit.ui.view.DamiHomeView
import com.loan.dami.credit.ui.view.DamiIDCardView
import com.loan.dami.credit.ui.view.DamiJobView
import com.loan.dami.credit.ui.view.DamiLoanConfirmView
import com.loan.dami.credit.ui.view.DamiLoanListView
import com.loan.dami.credit.ui.view.DamiLoginView
import com.loan.dami.credit.ui.view.DamiMineView
import com.loan.dami.credit.ui.view.DamiPersonalView
import com.loan.dami.credit.ui.view.DamiPrivacyView
import com.loan.dami.credit.ui.view.DamiQualityView
import com.loan.dami.credit.ui.view.DamiRecreditView
import com.loan.dami.credit.ui.view.DamiSettingView
import com.loan.dami.credit.ui.view.DamiStartView
import com.loan.dami.credit.ui.view.DamiUrlView

val LocalDamiController = staticCompositionLocalOf<NavHostController> { error("controller error") }

@Composable
fun DamiNavigation() {
    val controller = rememberNavController()
    CompositionLocalProvider(LocalDamiController provides controller) {
        DamiDreditTheme {
            NavHost(controller, DamiStartRoute){
                composable<DamiStartRoute> { DamiStartView() }
                composable<DamiPrivacyRoute> { DamiPrivacyView() }
                composable<DamiHomeRoute> { DamiHomeView() }
                composable<DamiQualityRoute> { DamiQualityView() }
                composable<DamiLoginRoute> { DamiLoginView() }
                composable<DamiSettingRoute> { DamiSettingView() }
                composable<DamiCameraRoute> { DamiCameraView() }
                composable<DamiMineRoute> { DamiMineView(it.toRoute()) }
                composable<DamiLoanListRoute> { DamiLoanListView(it.toRoute()) }
                composable<DamiEKYCRoute> { DamiEKYCView(it.toRoute()) }
                composable<DamiIDCardRoute> { DamiIDCardView(it.toRoute()) }
                composable<DamiPersonalRoute> { DamiPersonalView(it.toRoute()) }
                composable<DamiJobRoute> { DamiJobView(it.toRoute()) }
                composable<DamiExtRoute> { DamiExtView(it.toRoute()) }
                composable<DamiBankRoute> { DamiBankView(it.toRoute()) }
                composable<DamiLoanConfirmRoute> { DamiLoanConfirmView(it.toRoute()) }
                composable<DamiBankListRoute> { DamiBankListView(it.toRoute()) }
                composable<DamiRecreditRoute> { DamiRecreditView(it.toRoute()) }
                composable<DamiUrlRoute> { DamiUrlView(it.toRoute()) }
            }
        }
    }
}