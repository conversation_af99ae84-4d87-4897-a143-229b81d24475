package com.loan.dami.credit.ui.view

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.config.DamiCoil
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiRouteUtils
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiSizeText
import com.loan.dami.credit.config.DamiUtils.dates
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiLoanConfirmModel
import com.loan.dami.credit.ui.dialog.DamiDialog
import com.loan.dami.credit.ui.dialog.DamiLoanConfirmDialog
import com.loan.dami.credit.ui.dialog.DamiVerifyDialog
import com.loan.dami.credit.ui.route.DamiBankListRoute
import com.loan.dami.credit.ui.route.DamiLoanConfirmRoute
import kotlinx.coroutines.delay

@Composable
fun DamiLoanConfirmView(route: DamiLoanConfirmRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current

    val model = viewModel { DamiLoanConfirmModel(route) }
    val state by model.state.collectAsState()

    LaunchedEffect(model) { model.loanConfirmData(controller) }

    var detailState by remember { mutableStateOf(false) }
    if (detailState) {
        state.bean?.let { DamiLoanConfirmDialog(it){ detailState = false } }
    }

    state.dialog?.let {
        DamiDialog(
            title = "Tips",
            content = it.text,
            left = "Cancel",
            right = "Change Account"
        ) {
            model.closeDialog()
            if (it == 2) {
                controller.navigate(DamiBankListRoute(route.id, state.bean?.orderNo ?: ""))
            }
        }
    }

    var url by remember { mutableStateOf<String?>(null) }

    url?.let {
        DamiVerifyDialog(it) {
            url = null
            if (it == 1) {
                controller.popBackStack(route, true)
            }
        }
    }

    val back = {
        model.dialogVerify(controller, 5, route.id) {
            if (it.dialog?.image_source?.isNotEmpty() == true) {
                url = it.dialog.image_source
            } else {
                controller.popBackStack(route, true)
            }
        }
    }

    BackHandler { back() }

    DamiScrollView(model, title = "Loan Confirmation", bottomBarColor = Color.White, onBack = { back() }, bottomBar = {
        Row(modifier = Modifier.padding(start = 20.dp, end = 20.dp, top = 10.dp)) {
            DamiImage(
                modifier = Modifier
                    .clip(CircleShape)
                    .clickable { model.agree() }
                    .padding(6.dp)
                    .size(16.dp),
                id = if (state.agree) R.mipmap.icon_login_select_s else R.mipmap.icon_login_select_n
            )
            Text(modifier = Modifier.padding(start = 2.dp, top = 6.dp), text = buildAnnotatedString {
                append("I have read and agree to the ")
                withLink(link = LinkAnnotation.Clickable(tag = "", styles = TextLinkStyles(style = SpanStyle(color = Color(0xffffa724), textDecoration = TextDecoration.Underline))) {
                    DamiRouteUtils.navigateWeb(controller, state.bean?.loan_agreement_url)
                }) {
                    append("Loan Agreement")
                }
            }, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff6a6a6b))
        }
        Text(
            modifier = Modifier
                .padding(horizontal = 20.dp, vertical = 10.dp)
                .fillMaxWidth()
                .background(Color(0xff016b76), shape = CircleShape)
                .clickDelay { model.loanConfirm(context, controller) }
                .padding(14.dp),
            text = "Confirm",
            fontSize = 16.sp,
            lineHeight = 20.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }) {
        Column(
            Modifier
                .background(Color(0xff016b76))
                .padding(horizontal = 20.dp)
                .background(brush = Brush.verticalGradient(listOf(Color(0xffe8a78e), Color(0xffe09578))), shape = RoundedCornerShape(16.dp))
                .padding(horizontal = 20.dp, vertical = 16.dp)
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                DamiSizeText(modifier = Modifier.weight(1f), text = state.bean?.displayAmount ?: "--", fontSize = 32.sp, lineHeight = 38.sp, color = Color.White, fontWeight = FontWeight.Medium)
                Text(
                    modifier = Modifier
                        .border(1.dp, Color.White, RoundedCornerShape(10.dp))
                        .background(Color(0xffec8944), RoundedCornerShape(10.dp))
                        .clickDelay { controller.navigate(DamiBankListRoute(route.id, state.bean?.orderNo ?: "")) }
                        .padding(horizontal = 6.dp, vertical = 4.dp),
                    text = "Change Acount",
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
            }
            Text(text = "Available up to(₱)", fontSize = 12.sp, lineHeight = 16.sp, color = Color.White)
            Row(
                modifier = Modifier
                    .padding(top = 2.dp)
                    .background(brush = Brush.horizontalGradient(listOf(Color.Transparent, Color(0x30ffffff), Color.Transparent)))
                    .padding(top = 10.dp)
            ) {
                Text(modifier = Modifier.weight(1f), text = "Receipt Account", fontSize = 12.sp, lineHeight = 18.sp, color = Color.White)
                Text(text = "Loan Term", fontSize = 12.sp, lineHeight = 18.sp, color = Color.White)
            }
            Row(
                modifier = Modifier
                    .background(brush = Brush.horizontalGradient(listOf(Color.Transparent, Color(0x30ffffff), Color.Transparent)))
                    .padding(bottom = 10.dp)
            ) {
                Text(modifier = Modifier.weight(1f), text = state.bean?.bankCard ?: "--", fontSize = 16.sp, lineHeight = 20.sp, fontWeight = FontWeight.Medium, color = Color.White)
                Text(text = state.bean?.displayTerm ?: "--", fontSize = 16.sp, fontWeight = FontWeight.Medium, lineHeight = 20.sp, color = Color.White)
            }
            state.bean?.countdownModule?.let { time ->
                LaunchedEffect(state.time) {
                    if (state.time > 0) {
                        delay(1000)
                        model.setTime(state.time - 1)
                    }
                }
                AnimatedVisibility(state.time > 0) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp)
                                .background(Color.White, RoundedCornerShape(10.dp))
                                .padding(12.dp), horizontalArrangement = Arrangement.Center
                        ) {
                            state.time.dates().forEachIndexed { i, s ->
                                if (i == 2) {
                                    Text(
                                        modifier = Modifier.padding(horizontal = 6.dp),
                                        text = s,
                                        fontSize = 30.sp,
                                        lineHeight = 36.sp,
                                        color = Color(0xff016b76),
                                        fontWeight = FontWeight.Bold
                                    )
                                } else {
                                    Text(
                                        modifier = Modifier
                                            .padding(horizontal = 6.dp)
                                            .background(Color(0xff016b76), RoundedCornerShape(5.dp))
                                            .padding(horizontal = 9.dp),
                                        text = s,
                                        fontSize = 30.sp,
                                        lineHeight = 36.sp,
                                        color = Color.White,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                        }
                        DamiImage(modifier = Modifier.size(31.dp, 19.dp), id = R.mipmap.icon_confirm_arrow)
                        Text(
                            modifier = Modifier.padding(top = 6.dp, start = 20.dp, end = 20.dp),
                            text = buildAnnotatedString {
                                append("${time.content} ")
                                withStyle(style = SpanStyle(color = Color(0xfffffda0), fontStyle = FontStyle.Italic, fontWeight = FontWeight.Medium)) {
                                    append(time.percent)
                                }
                            },
                            fontSize = 12.sp,
                            lineHeight = 16.sp,
                            color = Color.White,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }

        Column(
            modifier = Modifier
                .background(Color(0xff016b76))
                .padding(top = 16.dp)
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(top = 8.dp)
        ) {
            /*if (state.bean?.serviceFee?.isNotEmpty() == true && state.bean?.serviceFee != "0") {
                state.bean?.serviceFeeDetail?.let {
                    Column(
                        modifier = Modifier
                            .padding(start = 20.dp, top = 12.dp, end = 20.dp)
                            .border(1.dp, Color(0xff82c3bd), shape = RoundedCornerShape(10.dp))
                            .background(Color.White, RoundedCornerShape(10.dp))
                            .padding(horizontal = 12.dp)
                    ) {
                        Row(modifier = Modifier.padding(vertical = 16.dp), verticalAlignment = Alignment.CenterVertically) {
                            DamiCoil(modifier = Modifier.size(24.dp), model = state.bean?.productLogo)
                            Text(
                                modifier = Modifier.padding(start = 8.dp),
                                text = state.bean?.productName ?: "--",
                                fontSize = 16.sp,
                                lineHeight = 20.sp,
                                color = Color(0xff2a292a),
                                fontWeight = FontWeight.Medium
                            )
                        }
                        HorizontalDivider(modifier = Modifier.padding(bottom = 8.dp), color = Color(0xffd1d1d1))
                        it.forEach {
                            Row(modifier = Modifier.padding(vertical = 4.dp), verticalAlignment = Alignment.CenterVertically) {
                                Text(modifier = Modifier.weight(1f), text = it.text, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xffb0b0b0))
                                Text(text = it.value, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
                            }
                        }
                        HorizontalDivider(modifier = Modifier.padding(top = 8.dp), color = Color(0xffd1d1d1))
                        Row(modifier = Modifier.padding(vertical = 16.dp), verticalAlignment = Alignment.CenterVertically) {
                            Text(modifier = Modifier.weight(1f), text = "Upfront Service Fee", fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff6a6a6b), fontWeight = FontWeight.Medium)
                            Text(text = state.bean?.displayServiceFee ?: "--", fontSize = 18.sp, lineHeight = 22.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                        }
                    }
                }
            }*/

            state.bean?.loan_periods?.let {
                AnimatedVisibility(it.size > 1) {
                    Column(
                        modifier = Modifier
                            .padding(start = 20.dp, top = 12.dp, end = 20.dp)
                            .border(1.dp, Color(0xff82c3bd), shape = RoundedCornerShape(10.dp))
                            .background(Color.White, RoundedCornerShape(10.dp))
                            .padding(start = 4.dp, end = 4.dp, bottom = 8.dp)
                    ) {
                        Row(modifier = Modifier.padding(horizontal = 8.dp, vertical = 16.dp), verticalAlignment = Alignment.CenterVertically) {
                            DamiCoil(modifier = Modifier.size(24.dp), model = state.bean?.productLogo)
                            Text(modifier = Modifier.padding(start = 8.dp), text = "Loan Period", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                        }
                        HorizontalDivider(modifier = Modifier.padding(start = 8.dp, end = 8.dp, bottom = 8.dp), color = Color(0xffd1d1d1))
                        it.chunked(2).forEach { list ->
                            Row {
                                list.forEach {
                                    Text(
                                        modifier = Modifier
                                            .weight(1f)
                                            .padding(horizontal = 8.dp, vertical = 4.dp)
                                            .background(Color(if (it.selected == 1) 0xff016b76 else 0xffe7e7e7), RoundedCornerShape(5.dp))
                                            .clickDelay { model.loanConfirmData(controller, it.optional_period_type) }
                                            .padding(10.dp),
                                        text = it.text,
                                        fontSize = 12.sp,
                                        lineHeight = 14.sp,
                                        color = Color(if (it.selected == 1) 0xffffffff else 0xffb0b0b0),
                                        fontWeight = FontWeight.Bold,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }
                        }
                    }
                }
            }

            Column(
                modifier = Modifier
                    .padding(start = 20.dp, top = 12.dp, end = 20.dp)
                    .border(1.dp, Color(0xff82c3bd), shape = RoundedCornerShape(10.dp))
                    .background(Color.White, RoundedCornerShape(10.dp))
                    .padding(horizontal = 12.dp)
            ) {
                Row(modifier = Modifier.padding(vertical = 16.dp), verticalAlignment = Alignment.CenterVertically) {
                    Text(modifier = Modifier.weight(1f), text = "Repayment Schedule", fontSize = 16.sp, lineHeight = 20.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                    /*Text(text = "${state.bean?.repay_plans?.size ?: 0} instalments", fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2e9a6d), fontWeight = FontWeight.Medium)*/
                    Text(
                        modifier = Modifier
                            .background(Color(0xff016b76), CircleShape)
                            .clickDelay { detailState = true }
                            .padding(horizontal = 15.dp, vertical = 3.dp),
                        text = "Details",
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        color = Color.White
                    )
                }
                HorizontalDivider(modifier = Modifier.padding(bottom = 8.dp), color = Color(0xffd1d1d1))
                state.bean?.repay_plans?.forEach { bean ->
                    Row(modifier = Modifier.padding(top = 8.dp)) {
                        Text(modifier = Modifier.weight(1f), text = bean.period_text, fontSize = 12.sp, lineHeight = 16.sp, color = Color(0xffb0b0b0))
                        Text(text = "Amount", fontSize = 12.sp, lineHeight = 16.sp, color = Color(0xffb0b0b0))
                    }
                    Row(modifier = Modifier.padding(vertical = 4.dp)) {
                        Text(modifier = Modifier.weight(1f), text = bean.pay_time, fontSize = 14.sp, lineHeight = 1.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                        Text(text = bean.repay_money, fontSize = 14.sp, lineHeight = 1.sp, color = Color(0xff2a292a), fontWeight = FontWeight.Medium)
                    }
                }
                state.bean?.note?.let {
                    HorizontalDivider(modifier = Modifier.padding(top = 8.dp), color = Color(0xffd1d1d1))
                    Text(modifier = Modifier.padding(vertical = 16.dp), text = it, fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xfff46e00), fontWeight = FontWeight.Medium)
                } ?: Spacer(modifier = Modifier.size(12.dp))
            }
        }
    }
}