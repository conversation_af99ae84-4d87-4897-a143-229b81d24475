package com.loan.dami.credit.ui.view

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.provider.ContactsContract
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.loan.dami.credit.LocalDamiController
import com.loan.dami.credit.R
import com.loan.dami.credit.bean.DamiExtBean
import com.loan.dami.credit.config.DamiFillImage
import com.loan.dami.credit.config.DamiImage
import com.loan.dami.credit.config.DamiInput
import com.loan.dami.credit.config.DamiScrollView
import com.loan.dami.credit.config.DamiTitle
import com.loan.dami.credit.config.clickDelay
import com.loan.dami.credit.model.DamiExtModel
import com.loan.dami.credit.ui.dialog.DamiSelectDialog
import com.loan.dami.credit.ui.dialog.DamiVerifyDialog
import com.loan.dami.credit.ui.route.DamiExtRoute

@Composable
fun DamiExtView(route: DamiExtRoute) {
    val context = LocalContext.current
    val controller = LocalDamiController.current

    val model = viewModel { DamiExtModel(route) }
    val state by model.state.collectAsState()

    LaunchedEffect(model) { model.extData(controller) }

    val activityResult = rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            model.setExtData(context, result)
        }
    }

    var url by remember { mutableStateOf<String?>(null) }

    url?.let {
        DamiVerifyDialog(it) {
            url = null
            if (it == 1) {
                controller.popBackStack(route, true)
            }
        }
    }

    val back = {
        model.dialogVerify(controller, 4, route.id) {
            if (it.dialog?.image_source?.isNotEmpty() == true) {
                url = it.dialog.image_source
            } else {
                controller.popBackStack()
            }
        }
    }

    BackHandler { back() }

    DamiScrollView(model, title = "Emergency Contacts", onBack = { back() }, bottomBar = {
        Text(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
                .background(Color(0xff016b76), shape = CircleShape)
                .clickDelay { model.saveExtData(context, controller) }
                .padding(14.dp),
            text = "Save",
            fontSize = 16.sp,
            lineHeight = 20.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }) {
        Box(
            modifier = Modifier
                .background(Color(0xff016b76))
                .padding(horizontal = 20.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            DamiFillImage(id = R.mipmap.top_bg_b)
            Text(
                modifier = Modifier.padding(horizontal = 20.dp),
                text = "We will protect your personal\ninformation form disclosure",
                fontSize = 16.sp,
                lineHeight = 19.sp,
                color = Color(0xff2a292a),
                fontWeight = FontWeight.Medium
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xff016b76))
                .padding(top = 16.dp)
                .background(Color(0xffecf5fa), shape = RoundedCornerShape(topEnd = 30.dp))
                .padding(vertical = 12.dp)
        ) {
            state.list?.forEachIndexed { index, bean ->
                DamiExtItem(bean, index, state.enable) {
                    state.index = index
                    try {
                        activityResult.launch(Intent(Intent.ACTION_PICK).apply { type = ContactsContract.CommonDataKinds.Phone.CONTENT_TYPE })
                    } catch (_: Exception) {
                        model.setEnable(false)
                    }
                }
            }

        }
    }
}

@Composable
fun DamiExtItem(bean: DamiExtBean, index: Int, enable: Boolean, onClick: () -> Unit) {
    bean.init()
    val keyManager = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    var nameState by remember { mutableStateOf(bean.name) }
    var mobileState by remember { mutableStateOf(bean.mobile) }
    var typeState by remember { mutableStateOf(bean.typeText) }
    var dialogState by remember { mutableStateOf(false) }

    if (dialogState) {
        bean.dropdown?.also { data ->
            DamiSelectDialog(title = "Relationship", data = data.map { it.name }) {
                dialogState = false
                if (it != -1) {
                    typeState = data[it].name
                    bean.typeText = typeState
                    bean.relation = data[it].type
                }
            }
        }
    }

    DamiTitle(modifier = Modifier.padding(top = 8.dp, bottom = 6.dp), title = "Relationship with Emergency Contacts - ${index + 1}")
    Column(
        modifier = Modifier
            .padding(horizontal = 20.dp, vertical = 6.dp)
            .border(1.dp, Color(0xff82c3bd), RoundedCornerShape(4.dp))
            .background(Color.White, RoundedCornerShape(4.dp))
            .clickDelay {
                keyManager?.hide()
                focusManager.clearFocus()
                dialogState = true
            }
            .padding(12.dp)
    ) {
        Text(modifier = Modifier.padding(vertical = 4.dp), text = "Relationship", fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
        HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp), color = Color(0xffd1d1d1))
        DamiInput(
            value = typeState,
            onValueChange = {},
            enabled = false,
            endView = { DamiImage(modifier = Modifier.size(14.dp, 8.dp), id = R.mipmap.personal_arrow) }
        )
    }
    Column(
        modifier = Modifier
            .padding(horizontal = 20.dp, vertical = 6.dp)
            .border(1.dp, Color(0xff82c3bd), RoundedCornerShape(4.dp))
            .background(Color.White, RoundedCornerShape(4.dp))
            .clickDelay(enable = !enable) {
                keyManager?.hide()
                focusManager.clearFocus()
                onClick()
            }
            .padding(12.dp)
    ) {
        Text(modifier = Modifier.padding(vertical = 4.dp), text = "Contact Information", fontSize = 14.sp, lineHeight = 18.sp, color = Color(0xff2a292a))
        HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp), color = Color(0xffd1d1d1))
        Box(contentAlignment = Alignment.CenterEnd) {
            DamiInput(
                value = if (enable) nameState else bean.name,
                onValueChange = {
                    nameState = it
                    bean.name = it
                },
                placeholder = if (enable) "Please enter name" else "Please select",
                enabled = enable,
                imeAction = ImeAction.Next
            )
            DamiImage(modifier = Modifier.size(25.dp), id = R.mipmap.icon_contact)
        }
        HorizontalDivider(modifier = Modifier.padding(vertical = 12.dp), color = Color(0xffd1d1d1))
        DamiInput(
            value = if (enable) mobileState else bean.mobile,
            onValueChange = {
                mobileState = it
                bean.mobile = it
            },
            placeholder = if (enable) "Please enter mobile" else "Please select",
            enabled = enable,
            keyboardType = KeyboardType.Phone
        )
    }
}