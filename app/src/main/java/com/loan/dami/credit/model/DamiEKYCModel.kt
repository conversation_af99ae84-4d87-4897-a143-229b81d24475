package com.loan.dami.credit.model

import android.content.Context
import android.net.Uri
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavHostController
import com.loan.dami.credit.DamiApplication
import com.loan.dami.credit.api.DamiApi
import com.loan.dami.credit.bean.DamiEKYCBean
import com.loan.dami.credit.bean.DamiVerifyBean
import com.loan.dami.credit.config.DamiModel
import com.loan.dami.credit.config.DamiRequestUtils
import com.loan.dami.credit.config.DamiState
import com.loan.dami.credit.config.DamiUtils.compress
import com.loan.dami.credit.ui.route.DamiCameraRoute
import com.loan.dami.credit.ui.route.DamiEKYCRoute
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

data class DamiEKYCState(val bean: DamiVerifyBean? = null, val type: String = "", val url: String? = null, val ekyc: DamiEKYCBean? = null, val showType: Int = 1) : DamiState()

class DamiEKYCModel(private val route: DamiEKYCRoute) : DamiModel<DamiEKYCState>(DamiEKYCState()) {

    fun kycData(controller: NavHostController) {
        if (data.showType == 1) {
            emitData<DamiVerifyBean>(controller, request = { DamiApi.kycData(route.id) }) {
                setData(data.copy(bean = it))
            }
        }
    }

    fun setType(type: String) {
        showLoading()
        damiData(2, route.id, route.orderNo) {
            closeLoading()
            setData(data.copy(type = type, showType = 2))
        }
    }

    fun closeDialog(){
        setData(data.copy(url = null))
    }

    fun backClick(controller: NavHostController) {
        if (data.showType == 1) {
            dialogVerify(controller, 0, route.id) {
                if (it.dialog?.image_source?.isNotEmpty() == true) {
                    setData(data.copy(url = it.dialog.image_source))
                } else {
                    controller.popBackStack()
                }
            }
        } else {
            setData(data.copy(showType = data.showType - 1))
        }
    }

    fun photoResult(context: Context, controller: NavHostController, uri: Uri?) {
        uri?.let {
            showLoading()
            viewModelScope.launch(Dispatchers.IO) {
                val file = File("${context.filesDir.path}/photo.jpg")
                context.contentResolver.openInputStream(it).use { input ->
                    FileOutputStream(file).use { output ->
                        input?.copyTo(output)
                    }
                }
                val compress = file.compress(context).path
                uploadFile(controller, 1, compress)
            }
        }
    }

    fun navigateCamera(context: Context, controller: NavHostController) {
        DamiRequestUtils.requestCamera(context) {
            if (it) {
                DamiApplication.camearCall = { path ->
                    viewModelScope.launch {
                        showLoading()
                        withContext(Dispatchers.IO) {
                            val file = File(path)
                            val compress = file.compress(context).path
                            uploadFile(controller, 2, compress)
                        }
                    }
                }
                controller.navigate(DamiCameraRoute)
            }
        }
    }

    fun uploadFile(controller: NavHostController, imageSource: Int, path: String) {
        emitData<DamiEKYCBean>(controller, request = { DamiApi.uploadFile(11, imageSource, path, data.type) }) {
            setData(data.copy(ekyc = it, showType = 3))
        }
    }

    fun submitEKYCData(context: Context, controller: NavHostController) {
        data.ekyc?.apply {
            if (name.isNotEmpty() && id_number.isNotEmpty() && birthday.isNotEmpty()) {
                showLoading()
                flowEmit(
                    controller = controller,
                    request = { DamiApi.submitEKYCData(birthday, id_number, name, data.type) },
                    loading = false,
                    complete = { if (!it) closeLoading() },
                    callback = {
                        damiData(3, route.id, route.orderNo) {
                            damiVerify(context, controller, route.id) {
                                controller.popBackStack()
                            }
                        }
                    }
                )
            } else {
                toast("Please check your ID information correctly.")
            }
        }
    }
}