package com.loan.dami.credit.bean

import kotlinx.serialization.Serializable

@Serializable
data class DamiExtBean(
    var emergent: DamiExtBean? = null,
    var list: List<DamiExtBean>? = null,
    var dropdown: List<DamiTagBean>? = null,
    var mobile: String = "",
    var name: String = "",
    var number1: String = "",
    var typeText: String = "",
    var relation: String = "",
    var content: String = ""
){

    fun init(){
        if (relation.isNotEmpty()) {
            dropdown?.find { it.type == relation }?.name?.let { typeText = it }
        }
    }
}
