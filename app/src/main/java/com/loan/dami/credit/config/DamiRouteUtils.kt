package com.loan.dami.credit.config

import androidx.navigation.NavHostController
import com.loan.dami.credit.config.DamiUtils.productId
import com.loan.dami.credit.ui.route.DamiHomeRoute
import com.loan.dami.credit.ui.route.DamiLoanConfirmRoute
import com.loan.dami.credit.ui.route.DamiLoanListRoute
import com.loan.dami.credit.ui.route.DamiLoginRoute
import com.loan.dami.credit.ui.route.DamiRecreditRoute
import com.loan.dami.credit.ui.route.DamiSettingRoute
import com.loan.dami.credit.ui.route.DamiUrlRoute

object DamiRouteUtils {

    fun navigate(controller: NavHostController, url: String, function: () -> Unit) {
        if (DamiDataUtils.isLogin(controller)) {
            when {
                url.startsWith(DAMI_MAIN) -> controller.navigate(DamiHomeRoute)
                url.startsWith(DAMI_SETTING) -> controller.navigate(DamiSettingRoute)
                url.startsWith(DAMI_LOGIN) -> controller.navigate(DamiLoginRoute)
                url.startsWith(DAMI_LIST) -> controller.navigate(DamiLoanListRoute(0))
                url.startsWith(DAMI_LOAN) -> controller.navigate(DamiLoanConfirmRoute(url.productId()))
                url.startsWith(DAMI_RECREDIT) -> controller.navigate(DamiRecreditRoute(url.productId()))
                url.startsWith("http") -> navigateWeb(controller, url)
                else -> function()
            }
        }
    }

    fun loginWeb(controller: NavHostController, url: String?) {
        if (DamiDataUtils.isLogin(controller)) {
            navigateWeb(controller, url)
        }
    }

    fun navigateWeb(controller: NavHostController, url: String?) {
        if (!url.isNullOrBlank()) {
            controller.navigate(DamiUrlRoute(url))
        }
    }

    const val DAMI_MAIN = "ph://dami-credit/recession"
    const val DAMI_SETTING = "ph://dami-credit/overwhelm"
    const val DAMI_LOGIN = "ph://dami-credit/nightmare"
    const val DAMI_LIST = "ph://dami-credit/underline"
    const val DAMI_VERIFY = "ph://dami-credit/champagne"
    const val DAMI_RECREDIT = "ph://dami-credit/automatic"
    const val DAMI_START = "ph://dami-credit/pollution"
    const val DAMI_LOAN = "ph://dami-credit/talkative"

}