package com.loan.dami.credit.ui.dialog

import androidx.compose.foundation.clickable
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.window.Dialog
import com.loan.dami.credit.bean.DamiAlertBean
import com.loan.dami.credit.config.DamiFillCoil

@Composable
fun DamiImageDialog(bean: DamiAlertBean, onCall: (Int) -> Unit) {
    Dialog(onDismissRequest = { onCall(0) }) {
        DamiFillCoil(modifier = Modifier.clickable { onCall(1) }, color = Color.Transparent, model = bean.img_url)
    }
}