<?xml version="1.0" encoding="utf-8"?>
<resources>

        <string name="livenessHomePromptFrontalFaceText">Please show your frontal face</string>
        <string name="livenessHomePromptFaceEreaText">Invalid face area for detection</string>
        <string name="livenessHomePromptBrighterText">Please under brighter lights conditions</string>
        <string name="livenessHomePromptDarkerText">Please under less bright lights conditions</string>
        <string name="livenessHomePromptCloserText">Please come closer</string>
        <string name="livenessHomePromptFurtherText">Please be a little farther</string>
        <string name="livenessHomePromptNoBacklightingText">Please avoid side lighting and backlighting</string>
        <string name="livenessHomePromptFrontalFaceInBoundingBoxText">Please fit your full-frontal face in the bounding box</string>
        <string name="livenessHomePromptNoEyesOcclusionText">Please avoid eyes occlusion</string>
        <string name="livenessHomePromptNoMouthOcclusionText">Please avoid mouth occlusion</string>
        <string name="livenessHomePromptStayStillText">Good, please stay still</string>
        <string name="livenessHomePromptWaitText"> Please wait for verification</string>
        <string name="livenessHomePromptShakeHeadText">Please turn your head to the left and right</string>
        <string name="livenessHomePromptNodText"> Please nod your head</string>
        <string name="livenessHomePromptOpenMouthText">Please open your mouth and close it</string>
        <string name="livenessHomePromptBlinkText"> Please blink your eyes </string>
        <string name="livenessExitTitlePromptText">Are you sure to quit?</string>
        <string name="livenessExitLeftPromptText">No</string>
        <string name="livenessExitRightPromptText">Yes</string>
        <string name="livenessRetryLeftPromptText">Try again</string>
        <string name="livenessRetryRightPromptText">Quit</string>
        <string name="livenessHomePromptVerticalText">Please pick up the phone and keep it vertical</string>
        <string name="livenessHomePromptTooBrightText">Lights too bright，please under less bright lights conditions</string>
        <string name="livenessHomePromptMultiplayerText">Please ensure single verification</string>
        <string name="livenessHomePromptCorrectActionText">The action is wrong</string>
        <string name="livenessHomePromptCoherenceText">Action is Incoherent</string>
        <string name="livenessHomePromptFaceLocationText">Keep the face in the frame</string>
        <string name="livenessHomePromptActionSpeedText">Action is too fast</string>
        <string name="livenessHomePromptMaskText">Take off the mask</string>
        <string name="livenessHomePromptFaceLostText">Keep the face in the frame</string>
        <string name="livenessHomePromptNoFaceText">Keep the face in the frame</string>
        <string name="livenessHomePromptOcclusionText">Avoid face occlusion</string>

</resources>