[versions]
agp = "8.9.1"
kotlin = "2.0.21"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
lifecycleRuntimeKtx = "2.8.7"
activityCompose = "1.10.1"
composeBom = "2025.04.01"

androidx-camera = "1.4.2"
androidx-datastore = "1.1.4"
androidx-navigation = "2.8.9"

google-services = "4.4.2"
google-crashlytics = "3.0.3"
firebase_analytics = "22.4.0"
firebase_config = "22.1.0"
firebase_crashlytics = "19.4.2"
firebase_messaging = "24.1.1"
ktor-version = "3.1.1"
coil-version = "3.0.4"
views-version = "1.7.0.1"
napier-version = "2.6.1"
qrose-version = "1.0.1"
play-services-ads = "18.2.0"
play-services-auth = "21.3.0"
app-update = "2.1.0"
installreferrer-version = "2.2"
homereferrer-version = "1.0.0.7"
review-version = "2.0.2"
xxpermission-version = "21.0"
compressor-version = "3.0.1"
compose-wheel-picker = "1.0.0-rc02"
adjust-android = "5.2.0"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }

androidx-datastore = { module = "androidx.datastore:datastore", version.ref = "androidx-datastore" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "androidx-datastore" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "androidx-navigation" }
androidx-camera = { module = "androidx.camera:camera-camera2", version.ref = "androidx-camera" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "androidx-camera" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "androidx-camera" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics-ktx", version.ref = "firebase_analytics" }
firebase-config = { module = "com.google.firebase:firebase-config-ktx", version.ref = "firebase_config" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics-ktx", version.ref = "firebase_crashlytics" }
firebase-messaging-directboot = { module = "com.google.firebase:firebase-messaging-directboot", version.ref = "firebase_messaging" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging-ktx", version.ref = "firebase_messaging" }
ktor-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor-version" }
ktor-encoding = { module = "io.ktor:ktor-client-encoding", version.ref = "ktor-version" }
ktor-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor-version" }
ktor-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor-version" }
ktor-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor-version" }
ktor-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor-version" }
coil-compose = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coil-version" }
coil-network = { module = "io.coil-kt.coil3:coil-network-ktor3", version.ref = "coil-version" }
views = { module = "io.github.ltttttttttttt:ComposeViews", version.ref = "views-version" }
napier = { module = "io.github.aakira:napier", version.ref = "napier-version" }
qrose = { module = "io.github.alexzhirkevich:qrose", version.ref = "qrose-version" }
play-services-ads-identifier = { module = "com.google.android.gms:play-services-ads-identifier", version.ref = "play-services-ads" }
play-services-auth = { module = "com.google.android.gms:play-services-auth", version.ref = "play-services-auth" }
app-update = { module = "com.google.android.play:app-update-ktx", version.ref = "app-update" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer-version" }
homereferrer = { module = "com.miui.referrer:homereferrer", version.ref = "homereferrer-version" }
review = { module = "com.google.android.play:review-ktx", version.ref = "review-version" }
xxpermissions = { module = "com.github.getActivity:XXPermissions", version.ref = "xxpermission-version" }
compressor = { module = "id.zelory:compressor", version.ref = "compressor-version" }
compose-wheel-picker = { module = "com.github.zj565061763:compose-wheel-picker", version.ref = "compose-wheel-picker" }
adjust-android = { module = "com.adjust.sdk:adjust-android", version.ref = "adjust-android" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-plugin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
googleServices = { id = "com.google.gms.google-services", version.ref = "google-services" }
googleCrashlytics = { id = "com.google.firebase.crashlytics", version.ref = "google-crashlytics" }
